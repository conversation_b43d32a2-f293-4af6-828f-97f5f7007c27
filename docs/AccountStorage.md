# Solidity API

## AccountStorage

_AccountStorageコントラクト
     Accountデータのストレージ管理を行う
     CRUDのみを実装し、ビジネスロジックは含まない_

### accountLogicOnly

```solidity
modifier accountLogicOnly()
```

_AccountLogicコントラクトからのみ呼び出し可能を保証するmodifier_

### initialize

```solidity
function initialize(contract IContractManager contractManager, address accountLogicAddr) public
```

_初期化関数_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerアドレス |
| accountLogicAddr | address | AccountLogicコントラクトアドレス |

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | コントラクトバージョン |

### addAccountData

```solidity
function addAccountData(bytes32 key, string accountName) external
```

_アカウントの登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| key | bytes32 | マッピングのキーとなるアカウントID |
| accountName | string | アカウント名 |

### addAccountId

```solidity
function addAccountId(bytes32 key) external
```

_アカウントID登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| key | bytes32 | 追加対象のアカウントID |

### getAccountIdExistence

```solidity
function getAccountIdExistence(bytes32 key) external view returns (bool exists)
```

_発行者IDの存在フラグを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| key | bytes32 | 発行者ID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| exists | bool | 存在フラグ |

### addAccountIdExistence

```solidity
function addAccountIdExistence(bytes32 key, bool exists) external
```

_アカウント存在確認フラグ登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| key | bytes32 | 追加対象のアカウントID |
| exists | bool | true:存在 / false:削除 |

### addValidatorId

```solidity
function addValidatorId(bytes32 key, bytes32 validatorId) external
```

_アカウントに紐づくvalidatorIdの登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| key | bytes32 | マッピングのキーとなるアカウントID |
| validatorId | bytes32 |  |

### getValidatorId

```solidity
function getValidatorId(bytes32 key) external view returns (bytes32 validatorId)
```

_発行者データを設定する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| key | bytes32 | 発行者ID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | バリデータID |

### updateAccountName

```solidity
function updateAccountName(bytes32 key, string accountName) external
```

_アカウント名の変更_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| key | bytes32 | マッピングのキーとなるアカウントID |
| accountName | string | アカウント名 |

### getAccountStatus

```solidity
function getAccountStatus(bytes32 key) external view returns (bytes32 accountStatus)
```

_アカウントステータス_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| key | bytes32 | マッピングのキーとなるアカウントID |

### setAccountStatusAndReasonCode

```solidity
function setAccountStatusAndReasonCode(bytes32 key, bytes32 accountStatus, bytes32 reasonCode) external
```

_Accountの有効性を更新する(凍結 or アクティブ)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| key | bytes32 | マッピングのキーとなるアカウントID |
| accountStatus | bytes32 | アカウントステータス |
| reasonCode | bytes32 | 理由コード |

### setAccountStatus

```solidity
function setAccountStatus(bytes32 key, bytes32 accountStatus) external
```

_Accountの有効性を更新する(アクティブ)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| key | bytes32 | マッピングのキーとなるアカウントID |
| accountStatus | bytes32 | アカウントステータス |

### setTerminated

```solidity
function setTerminated(bytes32 key, bytes32 reasonCode) external
```

_アカウントのステータスを解約済みに更新する　TODO:他関数と統合する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| key | bytes32 | マッピングのキーとなるアカウントID |
| reasonCode | bytes32 | 理由コード |

### addZone

```solidity
function addZone(bytes32 key, uint16 zoneId) external
```

_連携済みzone情報の追加_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| key | bytes32 | マッピングのキーとなるアカウントID |
| zoneId | uint16 | zoneId |

### getAccountName

```solidity
function getAccountName(bytes32 key) public view returns (string accountName)
```

_アカウントステータス_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| key | bytes32 | マッピングのキーとなるアカウントID |

### getAccountBalance

```solidity
function getAccountBalance(bytes32 key) external view returns (uint256 balance)
```

_発行後の残高。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| key | bytes32 | 発行者ID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| balance | uint256 | 発行後の残高 |

### setAccountBalance

```solidity
function setAccountBalance(bytes32 key, uint256 balance) external
```

_Accountの有効性を更新する(アクティブ)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| key | bytes32 | マッピングのキーとなるアカウントID |
| balance | uint256 | 発行後の残高 |

### setApprove

```solidity
function setApprove(bytes32 ownerId, bytes32 spenderId, uint256 approvedAt, uint256 amount) external
```

_送金許可設定。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| ownerId | bytes32 | ownerId |
| spenderId | bytes32 | spenderId |
| approvedAt | uint256 | 支払い許可日時 |
| amount | uint256 | 許容額 |

### setAllowance

```solidity
function setAllowance(bytes32 ownerId, bytes32 spenderId, uint256 amount) external
```

_送金許可額の減額を行う。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| ownerId | bytes32 | ownerId |
| spenderId | bytes32 | spenderId |
| amount | uint256 | 送金額 |

### setAccountAll

```solidity
function setAccountAll(struct AccountsAll account) external
```

_バックアップ用に全発行者データを設定する（Admin権限必要）_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| account | struct AccountsAll | 全発行者データ |

### getAccountDataWithoutZoneId

```solidity
function getAccountDataWithoutZoneId(bytes32 key) external view returns (struct AccountDataWithoutZoneId accountDataWithoutZoneId)
```

_Accountの情報を返す。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| key | bytes32 | 発行者ID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataWithoutZoneId | struct AccountDataWithoutZoneId | アカウントデータ(zoneIdなし) |

### getAccountData

```solidity
function getAccountData(bytes32 key) external view returns (struct AccountData accountData)
```

### getAccountId

```solidity
function getAccountId(uint256 index) external view returns (bytes32 accountId, string err)
```

_IndexよりAccountIDを取得する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| index | uint256 | index |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |
| err | string |  |

### getAllowance

```solidity
function getAllowance(bytes32 accountId, bytes32 index) external view returns (uint256 allowance, uint256 approvedAt)
```

_アカウントの許可額を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | マッピングのキーとなるアカウントID |
| index | bytes32 | 許可対象のアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| allowance | uint256 |  |
| approvedAt | uint256 |  |

### getAllowanceList

```solidity
function getAllowanceList(bytes32 ownerId, uint256 offset, uint256 limit) external view returns (struct AccountApprovalAll[] approvalData, uint256 totalCount, string err)
```

_送金許可一覧照会 TODO:Core APIとのマッピング時に作成_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| ownerId | bytes32 | 送金許可元ID |
| offset | uint256 | オフセット |
| limit | uint256 | リミット |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| approvalData | struct AccountApprovalAll[] | 送金許可設定一覧 |
| totalCount | uint256 | 総数 |
| err | string | エラーメッセージ |

### getAccountCount

```solidity
function getAccountCount() external view returns (uint256 count)
```

_Accountの数を返却する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |

### getAccountZoneIdList

```solidity
function getAccountZoneIdList(bytes32 key) external view returns (uint16[] zoneIdList)
```

_アカウントに連携済みのzoneIdの取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| key | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneIdList | uint16[] | アカウントに連携済みのzoneIdのリスト |

### getAccountAll

```solidity
function getAccountAll(uint256 index) external view returns (struct AccountsAll account)
```

_limitとoffsetで指定したAccountsを一括取得する_

