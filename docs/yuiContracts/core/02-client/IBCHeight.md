# Solidity API

## IBCHeight

### toUint128

```solidity
function toUint128(struct Height.Data self) internal pure returns (uint128)
```

### fromUint128

```solidity
function fromUint128(uint128 height) internal pure returns (struct Height.Data)
```

### is<PERSON>ero

```solidity
function isZero(struct Height.Data self) internal pure returns (bool)
```

### lt

```solidity
function lt(struct Height.Data self, struct Height.Data other) internal pure returns (bool)
```

### lte

```solidity
function lte(struct Height.Data self, struct Height.Data other) internal pure returns (bool)
```

### eq

```solidity
function eq(struct Height.Data self, struct Height.Data other) internal pure returns (bool)
```

### gt

```solidity
function gt(struct Height.Data self, struct Height.Data other) internal pure returns (bool)
```

### gte

```solidity
function gte(struct Height.Data self, struct Height.Data other) internal pure returns (bool)
```

