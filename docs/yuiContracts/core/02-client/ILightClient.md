# Solidity API

## ILightClient

_This defines an interface for Light Client contract can be integrated with ibc-solidity.
You can register the Light Client contract that implements this through `registerClient` on IBCHandler._

### createClient

```solidity
function createClient(string clientId, bytes clientStateBytes, bytes consensusStateBytes) external returns (bytes32 clientStateCommitment, struct ConsensusStateUpdate update, bool ok)
```

_createClient creates a new client with the given state.
If succeeded, it returns a commitment for the initial state._

### getTimestampAtHeight

```solidity
function getTimestampAtHeight(string clientId, struct Height.Data height) external view returns (uint64, bool)
```

_getTimestampAtHeight returns the timestamp of the consensus state at the given height.
     The timestamp is nanoseconds since unix epoch._

### getLatestHeight

```solidity
function getLatestHeight(string clientId) external view returns (struct Height.Data, bool)
```

_getLatestHeight returns the latest height of the client state corresponding to `clientId`._

### getStatus

```solidity
function getStatus(string clientId) external view returns (enum ClientStatus)
```

_getStatus returns the status of the client corresponding to `clientId`._

### updateClient

```solidity
function updateClient(string clientId, bytes clientMessageBytes) external returns (bytes32 clientStateCommitment, struct ConsensusStateUpdate[] updates, bool ok)
```

_updateClient updates the client corresponding to `clientId`.
If succeeded, it returns a commitment for the updated state.
If there is no update for client state, this function should returns bytes32(0) as `clientStateCommitment`
If there are no updates for consensus state, this function should returns an empty array as `updates`.

NOTE: updateClient is intended to perform the followings:
1. verify a given client message(e.g. header)
2. check misbehaviour such like duplicate block height
3. if misbehaviour is found, update state accordingly and return
4. update state(s) with the client message
5. persist the state(s) on the host_

### verifyMembership

```solidity
function verifyMembership(string clientId, struct Height.Data height, uint64 delayTimePeriod, uint64 delayBlockPeriod, bytes proof, bytes prefix, bytes path, bytes value) external returns (bool)
```

_verifyMembership is a generic proof verification method which verifies a proof of the existence of a value at a given CommitmentPath at the specified height.
The caller is expected to construct the full CommitmentPath from a CommitmentPrefix and a standardized path (as defined in ICS 24)._

### verifyNonMembership

```solidity
function verifyNonMembership(string clientId, struct Height.Data height, uint64 delayTimePeriod, uint64 delayBlockPeriod, bytes proof, bytes prefix, bytes path) external returns (bool)
```

_verifyNonMembership is a generic proof verification method which verifies the absence of a given CommitmentPath at a specified height.
The caller is expected to construct the full CommitmentPath from a CommitmentPrefix and a standardized path (as defined in ICS 24)._

### getClientState

```solidity
function getClientState(string clientId) external view returns (bytes, bool)
```

_getClientState returns the clientState corresponding to `clientId`.
     If it's not found, the function returns false._

### getConsensusState

```solidity
function getConsensusState(string clientId, struct Height.Data height) external view returns (bytes, bool)
```

_getConsensusState returns the consensusState corresponding to `clientId` and `height`.
     If it's not found, the function returns false._

## ConsensusStateUpdate

```solidity
struct ConsensusStateUpdate {
  bytes32 consensusStateCommitment;
  struct Height.Data height;
}
```

## ClientStatus

```solidity
enum ClientStatus {
  Active,
  Expired,
  Frozen
}
```

