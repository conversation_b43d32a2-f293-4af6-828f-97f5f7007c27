# Solidity API

## IIBCChannelHandshake

### MsgChannelOpenInit

```solidity
struct MsgChannelOpenInit {
  string portId;
  struct Channel.Data channel;
}
```

### MsgChannelOpenTry

```solidity
struct MsgChannelOpenTry {
  string portId;
  struct Channel.Data channel;
  string counterpartyVersion;
  bytes proofInit;
  struct Height.Data proofHeight;
}
```

### MsgChannelOpenAck

```solidity
struct MsgChannelOpenAck {
  string portId;
  string channelId;
  string counterpartyVersion;
  string counterpartyChannelId;
  bytes proofTry;
  struct Height.Data proofHeight;
}
```

### MsgChannelOpenConfirm

```solidity
struct MsgChannelOpenConfirm {
  string portId;
  string channelId;
  bytes proofAck;
  struct Height.Data proofHeight;
}
```

### MsgChannelCloseInit

```solidity
struct MsgChannelCloseInit {
  string portId;
  string channelId;
}
```

### MsgChannelCloseConfirm

```solidity
struct MsgChannelCloseConfirm {
  string portId;
  string channelId;
  bytes proofInit;
  struct Height.Data proofHeight;
}
```

### GeneratedChannelIdentifier

```solidity
event GeneratedChannelIdentifier(string)
```

### channelOpenInit

```solidity
function channelOpenInit(struct IIBCChannelHandshake.MsgChannelOpenInit msg_) external returns (string channelId, string version)
```

_channelOpenInit is called by a module to initiate a channel opening handshake with a module on another chain._

### channelOpenTry

```solidity
function channelOpenTry(struct IIBCChannelHandshake.MsgChannelOpenTry msg_) external returns (string channelId, string version)
```

_channelOpenTry is called by a module to accept the first step of a channel opening handshake initiated by a module on another chain._

### channelOpenAck

```solidity
function channelOpenAck(struct IIBCChannelHandshake.MsgChannelOpenAck msg_) external
```

_channelOpenAck is called by the handshake-originating module to acknowledge the acceptance of the initial request by the counterparty module on the other chain._

### channelOpenConfirm

```solidity
function channelOpenConfirm(struct IIBCChannelHandshake.MsgChannelOpenConfirm msg_) external
```

_channelOpenConfirm is called by the counterparty module to close their end of the channel, since the other end has been closed._

### channelCloseInit

```solidity
function channelCloseInit(struct IIBCChannelHandshake.MsgChannelCloseInit msg_) external
```

_channelCloseInit is called by either module to close their end of the channel. Once closed, channels cannot be reopened._

### channelCloseConfirm

```solidity
function channelCloseConfirm(struct IIBCChannelHandshake.MsgChannelCloseConfirm msg_) external
```

_channelCloseConfirm is called by the counterparty module to close their end of the
channel, since the other end has been closed._

## IICS04SendPacket

### SendPacket

```solidity
event SendPacket(uint64 sequence, string sourcePort, string sourceChannel, struct Height.Data timeoutHeight, uint64 timeoutTimestamp, bytes data)
```

### sendPacket

```solidity
function sendPacket(string sourcePort, string sourceChannel, struct Height.Data timeoutHeight, uint64 timeoutTimestamp, bytes data) external returns (uint64)
```

_sendPacket is called by a module in order to send an IBC packet on a channel.
The packet sequence generated for the packet to be sent is returned. An error
is returned if one occurs. Also, `timeoutTimestamp` is given in nanoseconds since unix epoch._

## IICS04WriteAcknowledgement

### WriteAcknowledgement

```solidity
event WriteAcknowledgement(string destinationPortId, string destinationChannel, uint64 sequence, bytes acknowledgement)
```

### writeAcknowledgement

```solidity
function writeAcknowledgement(string destinationPortId, string destinationChannel, uint64 sequence, bytes acknowledgement) external
```

_writeAcknowledgement writes the packet execution acknowledgement to the state,
which will be verified by the counterparty chain using AcknowledgePacket._

## IIBCChannelRecvPacket

### MsgPacketRecv

```solidity
struct MsgPacketRecv {
  struct Packet.Data packet;
  bytes proof;
  struct Height.Data proofHeight;
}
```

### RecvPacket

```solidity
event RecvPacket(struct Packet.Data packet)
```

### recvPacket

```solidity
function recvPacket(struct IIBCChannelRecvPacket.MsgPacketRecv msg_) external
```

_recvPacket is called by a module in order to receive & process an IBC packet
sent on the corresponding channel end on the counterparty chain._

## IIBCChannelAcknowledgePacket

### MsgPacketAcknowledgement

```solidity
struct MsgPacketAcknowledgement {
  struct Packet.Data packet;
  bytes acknowledgement;
  bytes proof;
  struct Height.Data proofHeight;
}
```

### AcknowledgePacket

```solidity
event AcknowledgePacket(struct Packet.Data packet, bytes acknowledgement)
```

### acknowledgePacket

```solidity
function acknowledgePacket(struct IIBCChannelAcknowledgePacket.MsgPacketAcknowledgement msg_) external
```

_AcknowledgePacket is called by a module to process the acknowledgement of a
packet previously sent by the calling module on a channel to a counterparty
module on the counterparty chain. Its intended usage is within the ante
handler. AcknowledgePacket will clean up the packet commitment,
which is no longer necessary since the packet has been received and acted upon.
It will also increment NextSequenceAck in case of ORDERED channels._

## IIBCChannelPacketTimeout

### MsgTimeoutPacket

```solidity
struct MsgTimeoutPacket {
  struct Packet.Data packet;
  bytes proof;
  struct Height.Data proofHeight;
  uint64 nextSequenceRecv;
}
```

### MsgTimeoutOnClose

```solidity
struct MsgTimeoutOnClose {
  struct Packet.Data packet;
  bytes proofUnreceived;
  bytes proofClose;
  struct Height.Data proofHeight;
  uint64 nextSequenceRecv;
}
```

### TimeoutPacket

```solidity
event TimeoutPacket(struct Packet.Data packet)
```

### timeoutPacket

```solidity
function timeoutPacket(struct IIBCChannelPacketTimeout.MsgTimeoutPacket msg_) external
```

_TimeoutPacket is called by a module which originally attempted to send a
packet to a counterparty module, where the timeout height has passed on the
counterparty chain without the packet being committed, to prove that the
packet can no longer be executed and to allow the calling module to safely
perform appropriate state transitions. Its intended usage is within the
ante handler._

### timeoutOnClose

```solidity
function timeoutOnClose(struct IIBCChannelPacketTimeout.MsgTimeoutOnClose msg_) external
```

_TimeoutOnClose is called by a module in order to prove that the channel to
which an unreceived packet was addressed has been closed, so the packet will
never be received (even if the timeoutHeight has not yet been reached)._

## IICS04Wrapper

## IIBCChannelPacketSendRecv

## IIBCChannelPacket

