# Solidity API

## IBCChannelPacketTimeout

### timeoutPacket

```solidity
function timeoutPacket(struct IIBCChannelPacketTimeout.MsgTimeoutPacket msg_) external
```

_TimeoutPacket is called by a module which originally attempted to send a
packet to a counterparty module, where the timeout height has passed on the
counterparty chain without the packet being committed, to prove that the
packet can no longer be executed and to allow the calling module to safely
perform appropriate state transitions. Its intended usage is within the
ante handler._

### timeoutOnClose

```solidity
function timeoutOnClose(struct IIBCChannelPacketTimeout.MsgTimeoutOnClose msg_) external
```

_TimeoutOnClose is called by a module in order to prove that the channel to
which an unreceived packet was addressed has been closed, so the packet will
never be received (even if the timeoutHeight has not yet been reached)._

