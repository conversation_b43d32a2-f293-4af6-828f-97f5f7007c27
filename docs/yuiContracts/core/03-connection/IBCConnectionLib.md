# Solidity API

## IBCConnectionLib

### IBC_VERSION_IDENTIFIER

```solidity
string IBC_VERSION_IDENTIFIER
```

### ORDER_ORDERED

```solidity
string ORDER_ORDERED
```

### ORDER_UNORDERED

```solidity
string ORDER_UNORDERED
```

### defaultIBCVersion

```solidity
function defaultIBCVersion() internal pure returns (struct Version.Data)
```

_defaultIBCVersion returns the latest supported version of IBC used in connection version negotiation_

### setSupportedVersions

```solidity
function setSupportedVersions(struct Version.Data[] supportedVersions, struct Version.Data[] dst) internal
```

_setSupportedVersions sets the supported versions to a given array.

NOTE: `versions` must be an empty array_

### isSupportedVersion

```solidity
function isSupportedVersion(struct Version.Data[] supportedVersions, struct Version.Data version) internal pure returns (bool)
```

_isSupportedVersion returns true if the proposed version has a matching version
identifier and its entire feature set is supported or the version identifier
supports an empty feature set._

### verifyProposedVersion

```solidity
function verifyProposedVersion(struct Version.Data supportedVersion, struct Version.Data proposedVersion) internal pure returns (bool)
```

_verifyProposedVersion verifies that the entire feature set in the
proposed version is supported by this chain. If the feature set is
empty it verifies that this is allowed for the specified version
identifier._

### findSupportedVersion

```solidity
function findSupportedVersion(struct Version.Data version, struct Version.Data[] supportedVersions) internal pure returns (struct Version.Data supportedVersion, bool found)
```

_findSupportedVersion returns the version with a matching version identifier
if it exists. The returned boolean is true if the version is found and
false otherwise._

### pickVersion

```solidity
function pickVersion(struct Version.Data[] supportedVersions, struct Version.Data[] counterpartyVersions) internal pure returns (struct Version.Data)
```

_pickVersion iterates over the descending ordered set of compatible IBC
versions and selects the first version with a version identifier that is
supported by the counterparty. The returned version contains a feature
set with the intersection of the features supported by the source and
counterparty chains. If the feature set intersection is nil and this is
not allowed for the chosen version identifier then the search for a
compatible version continues. This function is called in the ConnOpenTry
handshake procedure.

CONTRACT: pickVersion must only provide a version that is in the
intersection of the supported versions and the counterparty versions._

### copyVersions

```solidity
function copyVersions(struct Version.Data[] src, struct Version.Data[] dst) internal
```

_copyVersions copies `src` to `dst`_

### newVersions

```solidity
function newVersions(struct Version.Data version) internal pure returns (struct Version.Data[] ret)
```

_newVersions returns a new array with a given version_

### verifySupportedFeature

```solidity
function verifySupportedFeature(struct Version.Data version, string feature) internal pure returns (bool)
```

_verifySupportedFeature takes in a version and feature string and returns
true if the feature is supported by the version and false otherwise._

