# Solidity API

## IBCConnectionSelfStateNoValidation

_IBCConnectionSelfStateNoValidation is an IBCConnection that does not validate the self client state in the connection handshake._

### validateSelfClient

```solidity
function validateSelfClient(bytes) public pure returns (bool)
```

_validateSelfClient always returns true_

### getSelfConsensusState

```solidity
function getSelfConsensusState(struct Height.Data, bytes hostConsensusStateProof) public pure returns (bytes)
```

_getSelfConsensusState gets the consensus state of the host chain.

NOTE: Developers can override this function to support an arbitrary EVM chain._

