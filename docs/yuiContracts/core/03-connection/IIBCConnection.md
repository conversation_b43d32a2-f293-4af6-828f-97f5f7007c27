# Solidity API

## IIBCConnection

### MsgConnectionOpenInit

```solidity
struct MsgConnectionOpenInit {
  string clientId;
  struct Counterparty.Data counterparty;
  struct Version.Data version;
  uint64 delayPeriod;
}
```

### MsgConnectionOpenTry

```solidity
struct MsgConnectionOpenTry {
  struct Counterparty.Data counterparty;
  uint64 delayPeriod;
  string clientId;
  bytes clientStateBytes;
  struct Version.Data[] counterpartyVersions;
  bytes proofInit;
  bytes proofClient;
  bytes proofConsensus;
  struct Height.Data proofHeight;
  struct Height.Data consensusHeight;
  bytes hostConsensusStateProof;
}
```

### MsgConnectionOpenAck

```solidity
struct MsgConnectionOpenAck {
  string connectionId;
  bytes clientStateBytes;
  struct Version.Data version;
  string counterpartyConnectionId;
  bytes proofTry;
  bytes proofClient;
  bytes proofConsensus;
  struct Height.Data proofHeight;
  struct Height.Data consensusHeight;
  bytes hostConsensusStateProof;
}
```

### MsgConnectionOpenConfirm

```solidity
struct MsgConnectionOpenConfirm {
  string connectionId;
  bytes proofAck;
  struct Height.Data proofHeight;
}
```

### GeneratedConnectionIdentifier

```solidity
event GeneratedConnectionIdentifier(string)
```

### connectionOpenInit

```solidity
function connectionOpenInit(struct IIBCConnection.MsgConnectionOpenInit msg_) external returns (string connectionId)
```

_connectionOpenInit initialises a connection attempt on chain A. The generated connection identifier
is returned._

### connectionOpenTry

```solidity
function connectionOpenTry(struct IIBCConnection.MsgConnectionOpenTry msg_) external returns (string)
```

_connectionOpenTry relays notice of a connection attempt on chain A to chain B (this
code is executed on chain B)._

### connectionOpenAck

```solidity
function connectionOpenAck(struct IIBCConnection.MsgConnectionOpenAck msg_) external
```

_connectionOpenAck relays acceptance of a connection open attempt from chain B back
to chain A (this code is executed on chain A)._

### connectionOpenConfirm

```solidity
function connectionOpenConfirm(struct IIBCConnection.MsgConnectionOpenConfirm msg_) external
```

_connectionOpenConfirm confirms opening of a connection on chain A to chain B, after
which the connection is open on both chains (this code is executed on chain B)._

