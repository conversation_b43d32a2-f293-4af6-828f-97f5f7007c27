# Solidity API

## IBCConnection

_IBCConnection is a contract that implements [ICS-3](https://github.com/cosmos/ibc/tree/main/spec/core/ics-003-connection-semantics)._

### connectionOpenInit

```solidity
function connectionOpenInit(struct IIBCConnection.MsgConnectionOpenInit msg_) external returns (string)
```

_connectionOpenInit initialises a connection attempt on chain A. The generated connection identifier
is returned._

### connectionOpenTry

```solidity
function connectionOpenTry(struct IIBCConnection.MsgConnectionOpenTry msg_) external returns (string)
```

_connectionOpenTry relays notice of a connection attempt on chain A to chain B (this
code is executed on chain B)._

### connectionOpenAck

```solidity
function connectionOpenAck(struct IIBCConnection.MsgConnectionOpenAck msg_) external
```

_connectionOpenAck relays acceptance of a connection open attempt from chain B back
to chain A (this code is executed on chain A)._

### connectionOpenConfirm

```solidity
function connectionOpenConfirm(struct IIBCConnection.MsgConnectionOpenConfirm msg_) external
```

_connectionOpenConfirm confirms opening of a connection on chain A to chain B, after
which the connection is open on both chains (this code is executed on chain B)._

### getCompatibleVersions

```solidity
function getCompatibleVersions() public pure virtual returns (struct Version.Data[])
```

_getCompatibleVersions returns the supported versions of the host chain._

