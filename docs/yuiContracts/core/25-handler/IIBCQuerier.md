# Solidity API

## IIBCQuerier

### getClientByType

```solidity
function getClientByType(string clientType) external view returns (address)
```

### getClientType

```solidity
function getClientType(string clientId) external view returns (string)
```

### getClient

```solidity
function getClient(string clientId) external view returns (address)
```

### getClientState

```solidity
function getClientState(string clientId) external view returns (bytes, bool)
```

### getConsensusState

```solidity
function getConsensusState(string clientId, struct Height.Data height) external view returns (bytes, bool)
```

### getConnection

```solidity
function getConnection(string connectionId) external view returns (struct ConnectionEnd.Data, bool)
```

### getChannel

```solidity
function getChannel(string portId, string channelId) external view returns (struct Channel.Data, bool)
```

### getNextSequenceSend

```solidity
function getNextSequenceSend(string portId, string channelId) external view returns (uint64)
```

### getNextSequenceRecv

```solidity
function getNextSequenceRecv(string portId, string channelId) external view returns (uint64)
```

### getNextSequenceAck

```solidity
function getNextSequenceAck(string portId, string channelId) external view returns (uint64)
```

### getPacketReceipt

```solidity
function getPacketReceipt(string portId, string channelId, uint64 sequence) external view returns (enum IBCChannelLib.PacketReceipt)
```

### getCommitmentPrefix

```solidity
function getCommitmentPrefix() external view returns (bytes)
```

### getCommitment

```solidity
function getCommitment(bytes32 hashedPath) external view returns (bytes32)
```

### getExpectedTimePerBlock

```solidity
function getExpectedTimePerBlock() external view returns (uint64)
```

