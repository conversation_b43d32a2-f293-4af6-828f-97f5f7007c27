# Solidity API

## IBCClientConnectionChannelHandler

_IBCClientConnectionChannelHandler is a handler implements ICS-02, ICS-03, and ICS-04_

### ibcClient

```solidity
address ibcClient
```

### ibcConnection

```solidity
address ibcConnection
```

### ibcChannelHandshake

```solidity
address ibcChannelHandshake
```

### ibcChannelPacketSendRecv

```solidity
address ibcChannelPacketSendRecv
```

### ibcChannelPacketTimeout

```solidity
address ibcChannelPacketTimeout
```

### constructor

```solidity
constructor(contract IIBCClient ibcClient_, contract IIBCConnection ibcConnection_, contract IIBCChannelHandshake ibcChannelHandshake_, contract IIBCChannelPacketSendRecv ibcChannelPacketSendRecv_, contract IIBCChannelPacketTimeout ibcChannelPacketTimeout_) internal
```

_The arguments of constructor must satisfy the followings:_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| ibcClient_ | contract IIBCClient | is the address of a contract that implements `IIBCClient`. |
| ibcConnection_ | contract IIBCConnection | is the address of a contract that implements `IIBCConnection`. |
| ibcChannelHandshake_ | contract IIBCChannelHandshake | is the address of a contract that implements `IIBCChannelHandshake`. |
| ibcChannelPacketSendRecv_ | contract IIBCChannelPacketSendRecv | is the address of a contract that implements `IICS04Wrapper + IIBCChannelPacketReceiver`. |
| ibcChannelPacketTimeout_ | contract IIBCChannelPacketTimeout | is the address of a contract that implements `IIBCChannelPacketTimeout`. |

### createClient

```solidity
function createClient(struct IIBCClient.MsgCreateClient) external returns (string)
```

### updateClient

```solidity
function updateClient(struct IIBCClient.MsgUpdateClient) external
```

### connectionOpenInit

```solidity
function connectionOpenInit(struct IIBCConnection.MsgConnectionOpenInit) external returns (string)
```

### connectionOpenTry

```solidity
function connectionOpenTry(struct IIBCConnection.MsgConnectionOpenTry) external returns (string)
```

### connectionOpenAck

```solidity
function connectionOpenAck(struct IIBCConnection.MsgConnectionOpenAck) external
```

### connectionOpenConfirm

```solidity
function connectionOpenConfirm(struct IIBCConnection.MsgConnectionOpenConfirm) external
```

### channelOpenInit

```solidity
function channelOpenInit(struct IIBCChannelHandshake.MsgChannelOpenInit) external returns (string, string)
```

### channelOpenTry

```solidity
function channelOpenTry(struct IIBCChannelHandshake.MsgChannelOpenTry) external returns (string, string)
```

### channelOpenAck

```solidity
function channelOpenAck(struct IIBCChannelHandshake.MsgChannelOpenAck) external
```

### channelOpenConfirm

```solidity
function channelOpenConfirm(struct IIBCChannelHandshake.MsgChannelOpenConfirm) external
```

### channelCloseInit

```solidity
function channelCloseInit(struct IIBCChannelHandshake.MsgChannelCloseInit) external
```

### channelCloseConfirm

```solidity
function channelCloseConfirm(struct IIBCChannelHandshake.MsgChannelCloseConfirm) external
```

### sendPacket

```solidity
function sendPacket(string, string, struct Height.Data, uint64, bytes) external returns (uint64)
```

### writeAcknowledgement

```solidity
function writeAcknowledgement(string, string, uint64, bytes) external
```

### recvPacket

```solidity
function recvPacket(struct IIBCChannelRecvPacket.MsgPacketRecv) external
```

### acknowledgePacket

```solidity
function acknowledgePacket(struct IIBCChannelAcknowledgePacket.MsgPacketAcknowledgement) external
```

### timeoutPacket

```solidity
function timeoutPacket(struct IIBCChannelPacketTimeout.MsgTimeoutPacket) external
```

### timeoutOnClose

```solidity
function timeoutOnClose(struct IIBCChannelPacketTimeout.MsgTimeoutOnClose) external
```

### doFallback

```solidity
function doFallback(address impl) internal virtual
```

