# Solidity API

## IBCHandler

### constructor

```solidity
constructor(contract IIBCClient ibcClient_, contract IIBCConnection ibcConnection_, contract IIBCChannelHandshake ibcChannelHandshake_, contract IIBCChannelPacketSendRecv ibcChannelPacketSendRecv_, contract IIBCChannelPacketTimeout ibcChannelPacketTimeout_) internal
```

_The arguments of constructor must satisfy the followings:_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| ibcClient_ | contract IIBCClient | is the address of a contract that implements `IIBCClient`. |
| ibcConnection_ | contract IIBCConnection | is the address of a contract that implements `IIBCConnection`. |
| ibcChannelHandshake_ | contract IIBCChannelHandshake | is the address of a contract that implements `IIBCChannelHandshake`. |
| ibcChannelPacketSendRecv_ | contract IIBCChannelPacketSendRecv | is the address of a contract that implements `IIBCChannelPacketSendRecv`. |
| ibcChannelPacketTimeout_ | contract IIBCChannelPacketTimeout | is the address of a contract that implements `IIBCChannelPacketTimeout`. |

