# Solidity API

## IIBCHostConfigurator

### setExpectedTimePerBlock

```solidity
function setExpectedTimePerBlock(uint64 expectedTimePerBlock_) external
```

_setExpectedTimePerBlock sets expected time per block._

### registerClient

```solidity
function registerClient(string clientType, contract ILightClient client) external
```

_registerClient registers a new client type into the client registry_

### bindPort

```solidity
function bindPort(string portId, contract IIBCModule moduleAddress) external
```

_bindPort binds to an unallocated port, failing if the port has already been allocated._

