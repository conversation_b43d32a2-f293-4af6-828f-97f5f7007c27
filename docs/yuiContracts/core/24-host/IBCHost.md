# Solidity API

## IBCHost

### DEFAULT_COMMITMENT_PREFIX

```solidity
bytes DEFAULT_COMMITMENT_PREFIX
```

### _getCommitmentPrefix

```solidity
function _getCommitmentPrefix() internal view virtual returns (bytes)
```

__getCommitmentPrefix returns the prefix of the commitment proof._

### checkAndGetClient

```solidity
function checkAndGetClient(string clientId) internal view returns (contract ILightClient)
```

_checkAndGetClient returns the client implementation for the given client ID._

