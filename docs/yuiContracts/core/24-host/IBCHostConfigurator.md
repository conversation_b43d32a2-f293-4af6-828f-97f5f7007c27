# Solidity API

## IBCHostConfigurator

_IBCHostConfigurator is a contract that provides the host configuration._

### _setExpectedTimePerBlock

```solidity
function _setExpectedTimePerBlock(uint64 expectedTimePerBlock_) internal virtual
```

### _registerClient

```solidity
function _registerClient(string clientType, contract ILightClient client) internal virtual
```

### _bindPort

```solidity
function _bindPort(string portId, contract IIBCModule moduleAddress) internal virtual
```

