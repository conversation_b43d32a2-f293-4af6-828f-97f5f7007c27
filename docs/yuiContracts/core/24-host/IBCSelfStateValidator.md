# Solidity API

## IBCSelfStateValidator

_IBCSelfStateValidator is an interface that validates the self client state in the connection handshake._

### validateSelfClient

```solidity
function validateSelfClient(bytes clientState) public view virtual returns (bool)
```

_validateSelfClient validates the client parameters for a client of the host chain.

NOTE: Developers can override this function to support an arbitrary EVM chain._

### getSelfConsensusState

```solidity
function getSelfConsensusState(struct Height.Data consensusHeight, bytes hostConsensusStateProof) public view virtual returns (bytes)
```

_getSelfConsensusState gets the consensus state of the host chain.

NOTE: Developers can override this function to support an arbitrary EVM chain._

