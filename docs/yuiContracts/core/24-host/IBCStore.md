# Solidity API

## IBCStore

### commitments

```solidity
mapping(bytes32 => bytes32) commitments
```

### clientRegistry

```solidity
mapping(string => address) clientRegistry
```

### clientTypes

```solidity
mapping(string => string) clientTypes
```

### clientImpls

```solidity
mapping(string => address) clientImpls
```

### connections

```solidity
mapping(string => struct ConnectionEnd.Data) connections
```

### channels

```solidity
mapping(string => mapping(string => struct Channel.Data)) channels
```

### nextSequenceSends

```solidity
mapping(string => mapping(string => uint64)) nextSequenceSends
```

### nextSequenceRecvs

```solidity
mapping(string => mapping(string => uint64)) nextSequenceRecvs
```

### nextSequenceAcks

```solidity
mapping(string => mapping(string => uint64)) nextSequenceAcks
```

### capabilities

```solidity
mapping(string => address) capabilities
```

### expectedTimePerBlock

```solidity
uint64 expectedTimePerBlock
```

### nextClientSequence

```solidity
uint64 nextClientSequence
```

### nextConnectionSequence

```solidity
uint64 nextConnectionSequence
```

### nextChannelSequence

```solidity
uint64 nextChannelSequence
```

