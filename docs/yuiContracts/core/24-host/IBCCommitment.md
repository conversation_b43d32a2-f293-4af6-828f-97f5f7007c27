# Solidity API

## IBCCommitment

### clientStatePath

```solidity
function clientStatePath(string clientId) internal pure returns (bytes)
```

### consensusStatePath

```solidity
function consensusStatePath(string clientId, uint64 revisionNumber, uint64 revisionHeight) internal pure returns (bytes)
```

### connectionPath

```solidity
function connectionPath(string connectionId) internal pure returns (bytes)
```

### channelPath

```solidity
function channelPath(string portId, string channelId) internal pure returns (bytes)
```

### packetCommitmentPath

```solidity
function packetCommitmentPath(string portId, string channelId, uint64 sequence) internal pure returns (bytes)
```

### packetAcknowledgementCommitmentPath

```solidity
function packetAcknowledgementCommitmentPath(string portId, string channelId, uint64 sequence) internal pure returns (bytes)
```

### packetReceiptCommitmentPath

```solidity
function packetReceiptCommitmentPath(string portId, string channelId, uint64 sequence) internal pure returns (bytes)
```

### nextSequenceRecvCommitmentPath

```solidity
function nextSequenceRecvCommitmentPath(string portId, string channelId) internal pure returns (bytes)
```

### clientStateCommitmentKey

```solidity
function clientStateCommitmentKey(string clientId) internal pure returns (bytes32)
```

### consensusStateCommitmentKey

```solidity
function consensusStateCommitmentKey(string clientId, uint64 revisionNumber, uint64 revisionHeight) internal pure returns (bytes32)
```

### connectionCommitmentKey

```solidity
function connectionCommitmentKey(string connectionId) internal pure returns (bytes32)
```

### channelCommitmentKey

```solidity
function channelCommitmentKey(string portId, string channelId) internal pure returns (bytes32)
```

### packetCommitmentKey

```solidity
function packetCommitmentKey(string portId, string channelId, uint64 sequence) internal pure returns (bytes32)
```

### packetAcknowledgementCommitmentKey

```solidity
function packetAcknowledgementCommitmentKey(string portId, string channelId, uint64 sequence) internal pure returns (bytes32)
```

### packetReceiptCommitmentKey

```solidity
function packetReceiptCommitmentKey(string portId, string channelId, uint64 sequence) internal pure returns (bytes32)
```

### nextSequenceRecvCommitmentKey

```solidity
function nextSequenceRecvCommitmentKey(string portId, string channelId) internal pure returns (bytes32)
```

