# Solidity API

## ProtoBufRuntime

### WireType

```solidity
enum WireType {
  Varint,
  Fixed64,
  Length<PERSON>elim,
  StartGroup,
  EndGroup,
  Fixed32
}
```

### WORD_LENGTH

```solidity
uint256 WORD_LENGTH
```

### HEADER_SIZE_LENGTH_IN_BYTES

```solidity
uint256 HEADER_SIZE_LENGTH_IN_BYTES
```

### BYTE_SIZE

```solidity
uint256 BYTE_SIZE
```

### REMAINING_LENGTH

```solidity
uint256 REMAINING_LENGTH
```

### OVERFLOW_MESSAGE

```solidity
string OVERFLOW_MESSAGE
```

### encodeStorage

```solidity
function encodeStorage(bytes location, bytes encoded) internal
```

_Encode to storage location using assembly to save storage space._

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| location | bytes | The location of storage |
| encoded | bytes | The encoded ProtoBuf bytes |

### decodeStorage

```solidity
function decodeStorage(bytes location) internal view returns (bytes)
```

_Decode storage location using assembly using the format in `encodeStorage`._

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| location | bytes | The location of storage |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes | The encoded bytes |

### copyBytes

```solidity
function copyBytes(uint256 src, uint256 dest, uint256 len) internal pure
```

_Fast memory copy of bytes using assembly._

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| src | uint256 | The source memory address |
| dest | uint256 | The destination memory address |
| len | uint256 | The length of bytes to copy |

### getMemoryAddress

```solidity
function getMemoryAddress(bytes r) internal pure returns (uint256)
```

_Use assembly to get memory address._

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | bytes | The in-memory bytes array |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The memory address of `r` |

### ceil

```solidity
function ceil(uint256 a, uint256 m) internal pure returns (uint256 r)
```

_Implement Math function of ceil_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| a | uint256 | The denominator |
| m | uint256 | The numerator |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | uint256 | The result of ceil(a/m) |

### _decode_uint32

```solidity
function _decode_uint32(uint256 p, bytes bs) internal pure returns (uint32, uint256)
```

_Decode integers_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The memory offset of `bs` |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint32 | The decoded integer |
| [1] | uint256 | The length of `bs` used to get decoded |

### _decode_uint64

```solidity
function _decode_uint64(uint256 p, bytes bs) internal pure returns (uint64, uint256)
```

_Decode integers_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The memory offset of `bs` |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint64 | The decoded integer |
| [1] | uint256 | The length of `bs` used to get decoded |

### _decode_int32

```solidity
function _decode_int32(uint256 p, bytes bs) internal pure returns (int32, uint256)
```

_Decode integers_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The memory offset of `bs` |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | int32 | The decoded integer |
| [1] | uint256 | The length of `bs` used to get decoded |

### _decode_int64

```solidity
function _decode_int64(uint256 p, bytes bs) internal pure returns (int64, uint256)
```

_Decode integers_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The memory offset of `bs` |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | int64 | The decoded integer |
| [1] | uint256 | The length of `bs` used to get decoded |

### _decode_enum

```solidity
function _decode_enum(uint256 p, bytes bs) internal pure returns (int64, uint256)
```

_Decode enum_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The memory offset of `bs` |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | int64 | The decoded enum's integer |
| [1] | uint256 | The length of `bs` used to get decoded |

### _decode_bool

```solidity
function _decode_bool(uint256 p, bytes bs) internal pure returns (bool, uint256)
```

_Decode enum_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The memory offset of `bs` |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bool | The decoded boolean |
| [1] | uint256 | The length of `bs` used to get decoded |

### _decode_sint32

```solidity
function _decode_sint32(uint256 p, bytes bs) internal pure returns (int32, uint256)
```

_Decode signed integers_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The memory offset of `bs` |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | int32 | The decoded integer |
| [1] | uint256 | The length of `bs` used to get decoded |

### _decode_sint64

```solidity
function _decode_sint64(uint256 p, bytes bs) internal pure returns (int64, uint256)
```

_Decode signed integers_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The memory offset of `bs` |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | int64 | The decoded integer |
| [1] | uint256 | The length of `bs` used to get decoded |

### _decode_string

```solidity
function _decode_string(uint256 p, bytes bs) internal pure returns (string, uint256)
```

_Decode string_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The memory offset of `bs` |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | The decoded string |
| [1] | uint256 | The length of `bs` used to get decoded |

### _decode_bytes

```solidity
function _decode_bytes(uint256 p, bytes bs) internal pure returns (bytes, uint256)
```

_Decode bytes array_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The memory offset of `bs` |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes | The decoded bytes array |
| [1] | uint256 | The length of `bs` used to get decoded |

### _decode_key

```solidity
function _decode_key(uint256 p, bytes bs) internal pure returns (uint256, enum ProtoBufRuntime.WireType, uint256)
```

_Decode ProtoBuf key_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The memory offset of `bs` |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The decoded field ID |
| [1] | enum ProtoBufRuntime.WireType | The decoded WireType specified in ProtoBuf |
| [2] | uint256 | The length of `bs` used to get decoded |

### _decode_varint

```solidity
function _decode_varint(uint256 p, bytes bs) internal pure returns (uint256, uint256)
```

_Decode ProtoBuf varint_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The memory offset of `bs` |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The decoded unsigned integer |
| [1] | uint256 | The length of `bs` used to get decoded |

### _decode_varints

```solidity
function _decode_varints(uint256 p, bytes bs) internal pure returns (int256, uint256)
```

_Decode ProtoBuf zig-zag encoding_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The memory offset of `bs` |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | int256 | The decoded signed integer |
| [1] | uint256 | The length of `bs` used to get decoded |

### _decode_uintf

```solidity
function _decode_uintf(uint256 p, bytes bs, uint256 sz) internal pure returns (uint256, uint256)
```

_Decode ProtoBuf fixed-length encoding_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The memory offset of `bs` |
| bs | bytes | The bytes array to be decoded |
| sz | uint256 |  |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The decoded unsigned integer |
| [1] | uint256 | The length of `bs` used to get decoded |

### _decode_fixed32

```solidity
function _decode_fixed32(uint256 p, bytes bs) internal pure returns (uint32, uint256)
```

`_decode_(s)fixed(32|64)` is the concrete implementation of `_decode_uintf`

### _decode_fixed64

```solidity
function _decode_fixed64(uint256 p, bytes bs) internal pure returns (uint64, uint256)
```

### _decode_sfixed32

```solidity
function _decode_sfixed32(uint256 p, bytes bs) internal pure returns (int32, uint256)
```

### _decode_sfixed64

```solidity
function _decode_sfixed64(uint256 p, bytes bs) internal pure returns (int64, uint256)
```

### _decode_lendelim

```solidity
function _decode_lendelim(uint256 p, bytes bs) internal pure returns (bytes, uint256)
```

_Decode bytes array_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The memory offset of `bs` |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes | The decoded bytes array |
| [1] | uint256 | The length of `bs` used to get decoded |

### _skip_field_decode

```solidity
function _skip_field_decode(enum ProtoBufRuntime.WireType wt, uint256 p, bytes bs) internal pure returns (uint256)
```

_Skip the decoding of a single field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| wt | enum ProtoBufRuntime.WireType | The WireType of the field |
| p | uint256 | The memory offset of `bs` |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The length of `bs` to skipped |

### _encode_key

```solidity
function _encode_key(uint256 x, enum ProtoBufRuntime.WireType wt, uint256 p, bytes bs) internal pure returns (uint256)
```

_Encode ProtoBuf key_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | uint256 | The field ID |
| wt | enum ProtoBufRuntime.WireType | The WireType specified in ProtoBuf |
| p | uint256 | The offset of bytes array `bs` |
| bs | bytes | The bytes array to encode |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The length of encoded bytes |

### _encode_varint

```solidity
function _encode_varint(uint256 x, uint256 p, bytes bs) internal pure returns (uint256)
```

_Encode ProtoBuf varint_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | uint256 | The unsigned integer to be encoded |
| p | uint256 | The offset of bytes array `bs` |
| bs | bytes | The bytes array to encode |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The length of encoded bytes |

### _encode_varints

```solidity
function _encode_varints(int256 x, uint256 p, bytes bs) internal pure returns (uint256)
```

_Encode ProtoBuf zig-zag encoding_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | int256 | The signed integer to be encoded |
| p | uint256 | The offset of bytes array `bs` |
| bs | bytes | The bytes array to encode |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The length of encoded bytes |

### _encode_bytes

```solidity
function _encode_bytes(bytes xs, uint256 p, bytes bs) internal pure returns (uint256)
```

_Encode ProtoBuf bytes_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| xs | bytes | The bytes array to be encoded |
| p | uint256 | The offset of bytes array `bs` |
| bs | bytes | The bytes array to encode |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The length of encoded bytes |

### _encode_string

```solidity
function _encode_string(string xs, uint256 p, bytes bs) internal pure returns (uint256)
```

_Encode ProtoBuf string_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| xs | string | The string to be encoded |
| p | uint256 | The offset of bytes array `bs` |
| bs | bytes | The bytes array to encode |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The length of encoded bytes |

### _encode_uint32

```solidity
function _encode_uint32(uint32 x, uint256 p, bytes bs) internal pure returns (uint256)
```

`_encode_(u)int(32|64)`, `_encode_enum` and `_encode_bool`
are concrete implementation of `_encode_varint`

### _encode_uint64

```solidity
function _encode_uint64(uint64 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_int32

```solidity
function _encode_int32(int32 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_int64

```solidity
function _encode_int64(int64 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_enum

```solidity
function _encode_enum(int32 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_bool

```solidity
function _encode_bool(bool x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sint32

```solidity
function _encode_sint32(int32 x, uint256 p, bytes bs) internal pure returns (uint256)
```

`_encode_sint(32|64)`, `_encode_enum` and `_encode_bool`
are the concrete implementation of `_encode_varints`

### _encode_sint64

```solidity
function _encode_sint64(int64 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_fixed32

```solidity
function _encode_fixed32(uint32 x, uint256 p, bytes bs) internal pure returns (uint256)
```

`_encode_(s)fixed(32|64)` is the concrete implementation of `_encode_uintf`

### _encode_fixed64

```solidity
function _encode_fixed64(uint64 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sfixed32

```solidity
function _encode_sfixed32(int32 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sfixed64

```solidity
function _encode_sfixed64(int64 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_uintf

```solidity
function _encode_uintf(uint256 x, uint256 p, bytes bs, uint256 sz) internal pure returns (uint256)
```

_Encode ProtoBuf fixed-length integer_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | uint256 | The unsigned integer to be encoded |
| p | uint256 | The offset of bytes array `bs` |
| bs | bytes | The bytes array to encode |
| sz | uint256 |  |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The length of encoded bytes |

### _encode_zigzag

```solidity
function _encode_zigzag(int256 i) internal pure returns (uint256)
```

_Encode ProtoBuf zig-zag signed integer_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| i | int256 | The unsigned integer to be encoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The encoded unsigned integer |

### _sz_lendelim

```solidity
function _sz_lendelim(uint256 i) internal pure returns (uint256)
```

_Estimate the length of encoded LengthDelim_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| i | uint256 | The length of LengthDelim |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The estimated encoded length |

### _sz_key

```solidity
function _sz_key(uint256 i) internal pure returns (uint256)
```

_Estimate the length of encoded ProtoBuf field ID_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| i | uint256 | The field ID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The estimated encoded length |

### _sz_varint

```solidity
function _sz_varint(uint256 i) internal pure returns (uint256)
```

_Estimate the length of encoded ProtoBuf varint_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| i | uint256 | The unsigned integer |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The estimated encoded length |

### _sz_uint32

```solidity
function _sz_uint32(uint32 i) internal pure returns (uint256)
```

`_sz_(u)int(32|64)` and `_sz_enum` are the concrete implementation of `_sz_varint`

### _sz_uint64

```solidity
function _sz_uint64(uint64 i) internal pure returns (uint256)
```

### _sz_int32

```solidity
function _sz_int32(int32 i) internal pure returns (uint256)
```

### _sz_int64

```solidity
function _sz_int64(int64 i) internal pure returns (uint256)
```

### _sz_enum

```solidity
function _sz_enum(int64 i) internal pure returns (uint256)
```

### _sz_sint32

```solidity
function _sz_sint32(int32 i) internal pure returns (uint256)
```

`_sz_sint(32|64)` and `_sz_enum` are the concrete implementation of zig-zag encoding

### _sz_sint64

```solidity
function _sz_sint64(int64 i) internal pure returns (uint256)
```

### _estimate_packed_repeated_uint32

```solidity
function _estimate_packed_repeated_uint32(uint32[] a) internal pure returns (uint256)
```

`_estimate_packed_repeated_(uint32|uint64|int32|int64|sint32|sint64)`

### _estimate_packed_repeated_uint64

```solidity
function _estimate_packed_repeated_uint64(uint64[] a) internal pure returns (uint256)
```

### _estimate_packed_repeated_int32

```solidity
function _estimate_packed_repeated_int32(int32[] a) internal pure returns (uint256)
```

### _estimate_packed_repeated_int64

```solidity
function _estimate_packed_repeated_int64(int64[] a) internal pure returns (uint256)
```

### _estimate_packed_repeated_sint32

```solidity
function _estimate_packed_repeated_sint32(int32[] a) internal pure returns (uint256)
```

### _estimate_packed_repeated_sint64

```solidity
function _estimate_packed_repeated_sint64(int64[] a) internal pure returns (uint256)
```

### _count_packed_repeated_varint

```solidity
function _count_packed_repeated_varint(uint256 p, uint256 len, bytes bs) internal pure returns (uint256)
```

### _decode_sol_bytesN_lower

```solidity
function _decode_sol_bytesN_lower(uint8 n, uint256 p, bytes bs) internal pure returns (bytes32, uint256)
```

_Decode Solidity integer and/or fixed-size bytes array, filling from lowest bit._

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| n | uint8 | The maximum number of bytes to read |
| p | uint256 | The offset of bytes array `bs` |
| bs | bytes | The bytes array to encode |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes32 | The bytes32 representation |
| [1] | uint256 | The number of bytes used to decode |

### _decode_sol_bytesN

```solidity
function _decode_sol_bytesN(uint8 n, uint256 p, bytes bs) internal pure returns (bytes32, uint256)
```

_Decode Solidity integer and/or fixed-size bytes array, filling from highest bit._

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| n | uint8 | The maximum number of bytes to read |
| p | uint256 | The offset of bytes array `bs` |
| bs | bytes | The bytes array to encode |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes32 | The bytes32 representation |
| [1] | uint256 | The number of bytes used to decode |

### _decode_sol_address

```solidity
function _decode_sol_address(uint256 p, bytes bs) internal pure returns (address, uint256)
```

### _decode_sol_bool

```solidity
function _decode_sol_bool(uint256 p, bytes bs) internal pure returns (bool, uint256)
```

### _decode_sol_uint

```solidity
function _decode_sol_uint(uint256 p, bytes bs) internal pure returns (uint256, uint256)
```

### _decode_sol_uintN

```solidity
function _decode_sol_uintN(uint8 n, uint256 p, bytes bs) internal pure returns (uint256, uint256)
```

### _decode_sol_uint8

```solidity
function _decode_sol_uint8(uint256 p, bytes bs) internal pure returns (uint8, uint256)
```

### _decode_sol_uint16

```solidity
function _decode_sol_uint16(uint256 p, bytes bs) internal pure returns (uint16, uint256)
```

### _decode_sol_uint24

```solidity
function _decode_sol_uint24(uint256 p, bytes bs) internal pure returns (uint24, uint256)
```

### _decode_sol_uint32

```solidity
function _decode_sol_uint32(uint256 p, bytes bs) internal pure returns (uint32, uint256)
```

### _decode_sol_uint40

```solidity
function _decode_sol_uint40(uint256 p, bytes bs) internal pure returns (uint40, uint256)
```

### _decode_sol_uint48

```solidity
function _decode_sol_uint48(uint256 p, bytes bs) internal pure returns (uint48, uint256)
```

### _decode_sol_uint56

```solidity
function _decode_sol_uint56(uint256 p, bytes bs) internal pure returns (uint56, uint256)
```

### _decode_sol_uint64

```solidity
function _decode_sol_uint64(uint256 p, bytes bs) internal pure returns (uint64, uint256)
```

### _decode_sol_uint72

```solidity
function _decode_sol_uint72(uint256 p, bytes bs) internal pure returns (uint72, uint256)
```

### _decode_sol_uint80

```solidity
function _decode_sol_uint80(uint256 p, bytes bs) internal pure returns (uint80, uint256)
```

### _decode_sol_uint88

```solidity
function _decode_sol_uint88(uint256 p, bytes bs) internal pure returns (uint88, uint256)
```

### _decode_sol_uint96

```solidity
function _decode_sol_uint96(uint256 p, bytes bs) internal pure returns (uint96, uint256)
```

### _decode_sol_uint104

```solidity
function _decode_sol_uint104(uint256 p, bytes bs) internal pure returns (uint104, uint256)
```

### _decode_sol_uint112

```solidity
function _decode_sol_uint112(uint256 p, bytes bs) internal pure returns (uint112, uint256)
```

### _decode_sol_uint120

```solidity
function _decode_sol_uint120(uint256 p, bytes bs) internal pure returns (uint120, uint256)
```

### _decode_sol_uint128

```solidity
function _decode_sol_uint128(uint256 p, bytes bs) internal pure returns (uint128, uint256)
```

### _decode_sol_uint136

```solidity
function _decode_sol_uint136(uint256 p, bytes bs) internal pure returns (uint136, uint256)
```

### _decode_sol_uint144

```solidity
function _decode_sol_uint144(uint256 p, bytes bs) internal pure returns (uint144, uint256)
```

### _decode_sol_uint152

```solidity
function _decode_sol_uint152(uint256 p, bytes bs) internal pure returns (uint152, uint256)
```

### _decode_sol_uint160

```solidity
function _decode_sol_uint160(uint256 p, bytes bs) internal pure returns (uint160, uint256)
```

### _decode_sol_uint168

```solidity
function _decode_sol_uint168(uint256 p, bytes bs) internal pure returns (uint168, uint256)
```

### _decode_sol_uint176

```solidity
function _decode_sol_uint176(uint256 p, bytes bs) internal pure returns (uint176, uint256)
```

### _decode_sol_uint184

```solidity
function _decode_sol_uint184(uint256 p, bytes bs) internal pure returns (uint184, uint256)
```

### _decode_sol_uint192

```solidity
function _decode_sol_uint192(uint256 p, bytes bs) internal pure returns (uint192, uint256)
```

### _decode_sol_uint200

```solidity
function _decode_sol_uint200(uint256 p, bytes bs) internal pure returns (uint200, uint256)
```

### _decode_sol_uint208

```solidity
function _decode_sol_uint208(uint256 p, bytes bs) internal pure returns (uint208, uint256)
```

### _decode_sol_uint216

```solidity
function _decode_sol_uint216(uint256 p, bytes bs) internal pure returns (uint216, uint256)
```

### _decode_sol_uint224

```solidity
function _decode_sol_uint224(uint256 p, bytes bs) internal pure returns (uint224, uint256)
```

### _decode_sol_uint232

```solidity
function _decode_sol_uint232(uint256 p, bytes bs) internal pure returns (uint232, uint256)
```

### _decode_sol_uint240

```solidity
function _decode_sol_uint240(uint256 p, bytes bs) internal pure returns (uint240, uint256)
```

### _decode_sol_uint248

```solidity
function _decode_sol_uint248(uint256 p, bytes bs) internal pure returns (uint248, uint256)
```

### _decode_sol_uint256

```solidity
function _decode_sol_uint256(uint256 p, bytes bs) internal pure returns (uint256, uint256)
```

### _decode_sol_int

```solidity
function _decode_sol_int(uint256 p, bytes bs) internal pure returns (int256, uint256)
```

### _decode_sol_intN

```solidity
function _decode_sol_intN(uint8 n, uint256 p, bytes bs) internal pure returns (int256, uint256)
```

### _decode_sol_bytes

```solidity
function _decode_sol_bytes(uint8 n, uint256 p, bytes bs) internal pure returns (bytes32, uint256)
```

### _decode_sol_int8

```solidity
function _decode_sol_int8(uint256 p, bytes bs) internal pure returns (int8, uint256)
```

### _decode_sol_int16

```solidity
function _decode_sol_int16(uint256 p, bytes bs) internal pure returns (int16, uint256)
```

### _decode_sol_int24

```solidity
function _decode_sol_int24(uint256 p, bytes bs) internal pure returns (int24, uint256)
```

### _decode_sol_int32

```solidity
function _decode_sol_int32(uint256 p, bytes bs) internal pure returns (int32, uint256)
```

### _decode_sol_int40

```solidity
function _decode_sol_int40(uint256 p, bytes bs) internal pure returns (int40, uint256)
```

### _decode_sol_int48

```solidity
function _decode_sol_int48(uint256 p, bytes bs) internal pure returns (int48, uint256)
```

### _decode_sol_int56

```solidity
function _decode_sol_int56(uint256 p, bytes bs) internal pure returns (int56, uint256)
```

### _decode_sol_int64

```solidity
function _decode_sol_int64(uint256 p, bytes bs) internal pure returns (int64, uint256)
```

### _decode_sol_int72

```solidity
function _decode_sol_int72(uint256 p, bytes bs) internal pure returns (int72, uint256)
```

### _decode_sol_int80

```solidity
function _decode_sol_int80(uint256 p, bytes bs) internal pure returns (int80, uint256)
```

### _decode_sol_int88

```solidity
function _decode_sol_int88(uint256 p, bytes bs) internal pure returns (int88, uint256)
```

### _decode_sol_int96

```solidity
function _decode_sol_int96(uint256 p, bytes bs) internal pure returns (int96, uint256)
```

### _decode_sol_int104

```solidity
function _decode_sol_int104(uint256 p, bytes bs) internal pure returns (int104, uint256)
```

### _decode_sol_int112

```solidity
function _decode_sol_int112(uint256 p, bytes bs) internal pure returns (int112, uint256)
```

### _decode_sol_int120

```solidity
function _decode_sol_int120(uint256 p, bytes bs) internal pure returns (int120, uint256)
```

### _decode_sol_int128

```solidity
function _decode_sol_int128(uint256 p, bytes bs) internal pure returns (int128, uint256)
```

### _decode_sol_int136

```solidity
function _decode_sol_int136(uint256 p, bytes bs) internal pure returns (int136, uint256)
```

### _decode_sol_int144

```solidity
function _decode_sol_int144(uint256 p, bytes bs) internal pure returns (int144, uint256)
```

### _decode_sol_int152

```solidity
function _decode_sol_int152(uint256 p, bytes bs) internal pure returns (int152, uint256)
```

### _decode_sol_int160

```solidity
function _decode_sol_int160(uint256 p, bytes bs) internal pure returns (int160, uint256)
```

### _decode_sol_int168

```solidity
function _decode_sol_int168(uint256 p, bytes bs) internal pure returns (int168, uint256)
```

### _decode_sol_int176

```solidity
function _decode_sol_int176(uint256 p, bytes bs) internal pure returns (int176, uint256)
```

### _decode_sol_int184

```solidity
function _decode_sol_int184(uint256 p, bytes bs) internal pure returns (int184, uint256)
```

### _decode_sol_int192

```solidity
function _decode_sol_int192(uint256 p, bytes bs) internal pure returns (int192, uint256)
```

### _decode_sol_int200

```solidity
function _decode_sol_int200(uint256 p, bytes bs) internal pure returns (int200, uint256)
```

### _decode_sol_int208

```solidity
function _decode_sol_int208(uint256 p, bytes bs) internal pure returns (int208, uint256)
```

### _decode_sol_int216

```solidity
function _decode_sol_int216(uint256 p, bytes bs) internal pure returns (int216, uint256)
```

### _decode_sol_int224

```solidity
function _decode_sol_int224(uint256 p, bytes bs) internal pure returns (int224, uint256)
```

### _decode_sol_int232

```solidity
function _decode_sol_int232(uint256 p, bytes bs) internal pure returns (int232, uint256)
```

### _decode_sol_int240

```solidity
function _decode_sol_int240(uint256 p, bytes bs) internal pure returns (int240, uint256)
```

### _decode_sol_int248

```solidity
function _decode_sol_int248(uint256 p, bytes bs) internal pure returns (int248, uint256)
```

### _decode_sol_int256

```solidity
function _decode_sol_int256(uint256 p, bytes bs) internal pure returns (int256, uint256)
```

### _decode_sol_bytes1

```solidity
function _decode_sol_bytes1(uint256 p, bytes bs) internal pure returns (bytes1, uint256)
```

### _decode_sol_bytes2

```solidity
function _decode_sol_bytes2(uint256 p, bytes bs) internal pure returns (bytes2, uint256)
```

### _decode_sol_bytes3

```solidity
function _decode_sol_bytes3(uint256 p, bytes bs) internal pure returns (bytes3, uint256)
```

### _decode_sol_bytes4

```solidity
function _decode_sol_bytes4(uint256 p, bytes bs) internal pure returns (bytes4, uint256)
```

### _decode_sol_bytes5

```solidity
function _decode_sol_bytes5(uint256 p, bytes bs) internal pure returns (bytes5, uint256)
```

### _decode_sol_bytes6

```solidity
function _decode_sol_bytes6(uint256 p, bytes bs) internal pure returns (bytes6, uint256)
```

### _decode_sol_bytes7

```solidity
function _decode_sol_bytes7(uint256 p, bytes bs) internal pure returns (bytes7, uint256)
```

### _decode_sol_bytes8

```solidity
function _decode_sol_bytes8(uint256 p, bytes bs) internal pure returns (bytes8, uint256)
```

### _decode_sol_bytes9

```solidity
function _decode_sol_bytes9(uint256 p, bytes bs) internal pure returns (bytes9, uint256)
```

### _decode_sol_bytes10

```solidity
function _decode_sol_bytes10(uint256 p, bytes bs) internal pure returns (bytes10, uint256)
```

### _decode_sol_bytes11

```solidity
function _decode_sol_bytes11(uint256 p, bytes bs) internal pure returns (bytes11, uint256)
```

### _decode_sol_bytes12

```solidity
function _decode_sol_bytes12(uint256 p, bytes bs) internal pure returns (bytes12, uint256)
```

### _decode_sol_bytes13

```solidity
function _decode_sol_bytes13(uint256 p, bytes bs) internal pure returns (bytes13, uint256)
```

### _decode_sol_bytes14

```solidity
function _decode_sol_bytes14(uint256 p, bytes bs) internal pure returns (bytes14, uint256)
```

### _decode_sol_bytes15

```solidity
function _decode_sol_bytes15(uint256 p, bytes bs) internal pure returns (bytes15, uint256)
```

### _decode_sol_bytes16

```solidity
function _decode_sol_bytes16(uint256 p, bytes bs) internal pure returns (bytes16, uint256)
```

### _decode_sol_bytes17

```solidity
function _decode_sol_bytes17(uint256 p, bytes bs) internal pure returns (bytes17, uint256)
```

### _decode_sol_bytes18

```solidity
function _decode_sol_bytes18(uint256 p, bytes bs) internal pure returns (bytes18, uint256)
```

### _decode_sol_bytes19

```solidity
function _decode_sol_bytes19(uint256 p, bytes bs) internal pure returns (bytes19, uint256)
```

### _decode_sol_bytes20

```solidity
function _decode_sol_bytes20(uint256 p, bytes bs) internal pure returns (bytes20, uint256)
```

### _decode_sol_bytes21

```solidity
function _decode_sol_bytes21(uint256 p, bytes bs) internal pure returns (bytes21, uint256)
```

### _decode_sol_bytes22

```solidity
function _decode_sol_bytes22(uint256 p, bytes bs) internal pure returns (bytes22, uint256)
```

### _decode_sol_bytes23

```solidity
function _decode_sol_bytes23(uint256 p, bytes bs) internal pure returns (bytes23, uint256)
```

### _decode_sol_bytes24

```solidity
function _decode_sol_bytes24(uint256 p, bytes bs) internal pure returns (bytes24, uint256)
```

### _decode_sol_bytes25

```solidity
function _decode_sol_bytes25(uint256 p, bytes bs) internal pure returns (bytes25, uint256)
```

### _decode_sol_bytes26

```solidity
function _decode_sol_bytes26(uint256 p, bytes bs) internal pure returns (bytes26, uint256)
```

### _decode_sol_bytes27

```solidity
function _decode_sol_bytes27(uint256 p, bytes bs) internal pure returns (bytes27, uint256)
```

### _decode_sol_bytes28

```solidity
function _decode_sol_bytes28(uint256 p, bytes bs) internal pure returns (bytes28, uint256)
```

### _decode_sol_bytes29

```solidity
function _decode_sol_bytes29(uint256 p, bytes bs) internal pure returns (bytes29, uint256)
```

### _decode_sol_bytes30

```solidity
function _decode_sol_bytes30(uint256 p, bytes bs) internal pure returns (bytes30, uint256)
```

### _decode_sol_bytes31

```solidity
function _decode_sol_bytes31(uint256 p, bytes bs) internal pure returns (bytes31, uint256)
```

### _decode_sol_bytes32

```solidity
function _decode_sol_bytes32(uint256 p, bytes bs) internal pure returns (bytes32, uint256)
```

### _encode_sol_address

```solidity
function _encode_sol_address(address x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint

```solidity
function _encode_sol_uint(uint256 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint8

```solidity
function _encode_sol_uint8(uint8 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint16

```solidity
function _encode_sol_uint16(uint16 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint24

```solidity
function _encode_sol_uint24(uint24 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint32

```solidity
function _encode_sol_uint32(uint32 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint40

```solidity
function _encode_sol_uint40(uint40 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint48

```solidity
function _encode_sol_uint48(uint48 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint56

```solidity
function _encode_sol_uint56(uint56 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint64

```solidity
function _encode_sol_uint64(uint64 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint72

```solidity
function _encode_sol_uint72(uint72 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint80

```solidity
function _encode_sol_uint80(uint80 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint88

```solidity
function _encode_sol_uint88(uint88 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint96

```solidity
function _encode_sol_uint96(uint96 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint104

```solidity
function _encode_sol_uint104(uint104 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint112

```solidity
function _encode_sol_uint112(uint112 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint120

```solidity
function _encode_sol_uint120(uint120 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint128

```solidity
function _encode_sol_uint128(uint128 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint136

```solidity
function _encode_sol_uint136(uint136 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint144

```solidity
function _encode_sol_uint144(uint144 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint152

```solidity
function _encode_sol_uint152(uint152 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint160

```solidity
function _encode_sol_uint160(uint160 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint168

```solidity
function _encode_sol_uint168(uint168 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint176

```solidity
function _encode_sol_uint176(uint176 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint184

```solidity
function _encode_sol_uint184(uint184 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint192

```solidity
function _encode_sol_uint192(uint192 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint200

```solidity
function _encode_sol_uint200(uint200 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint208

```solidity
function _encode_sol_uint208(uint208 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint216

```solidity
function _encode_sol_uint216(uint216 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint224

```solidity
function _encode_sol_uint224(uint224 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint232

```solidity
function _encode_sol_uint232(uint232 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint240

```solidity
function _encode_sol_uint240(uint240 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint248

```solidity
function _encode_sol_uint248(uint248 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_uint256

```solidity
function _encode_sol_uint256(uint256 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int

```solidity
function _encode_sol_int(int256 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int8

```solidity
function _encode_sol_int8(int8 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int16

```solidity
function _encode_sol_int16(int16 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int24

```solidity
function _encode_sol_int24(int24 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int32

```solidity
function _encode_sol_int32(int32 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int40

```solidity
function _encode_sol_int40(int40 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int48

```solidity
function _encode_sol_int48(int48 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int56

```solidity
function _encode_sol_int56(int56 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int64

```solidity
function _encode_sol_int64(int64 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int72

```solidity
function _encode_sol_int72(int72 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int80

```solidity
function _encode_sol_int80(int80 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int88

```solidity
function _encode_sol_int88(int88 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int96

```solidity
function _encode_sol_int96(int96 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int104

```solidity
function _encode_sol_int104(int104 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int112

```solidity
function _encode_sol_int112(int112 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int120

```solidity
function _encode_sol_int120(int120 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int128

```solidity
function _encode_sol_int128(int128 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int136

```solidity
function _encode_sol_int136(int136 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int144

```solidity
function _encode_sol_int144(int144 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int152

```solidity
function _encode_sol_int152(int152 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int160

```solidity
function _encode_sol_int160(int160 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int168

```solidity
function _encode_sol_int168(int168 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int176

```solidity
function _encode_sol_int176(int176 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int184

```solidity
function _encode_sol_int184(int184 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int192

```solidity
function _encode_sol_int192(int192 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int200

```solidity
function _encode_sol_int200(int200 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int208

```solidity
function _encode_sol_int208(int208 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int216

```solidity
function _encode_sol_int216(int216 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int224

```solidity
function _encode_sol_int224(int224 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int232

```solidity
function _encode_sol_int232(int232 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int240

```solidity
function _encode_sol_int240(int240 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int248

```solidity
function _encode_sol_int248(int248 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_int256

```solidity
function _encode_sol_int256(int256 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes1

```solidity
function _encode_sol_bytes1(bytes1 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes2

```solidity
function _encode_sol_bytes2(bytes2 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes3

```solidity
function _encode_sol_bytes3(bytes3 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes4

```solidity
function _encode_sol_bytes4(bytes4 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes5

```solidity
function _encode_sol_bytes5(bytes5 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes6

```solidity
function _encode_sol_bytes6(bytes6 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes7

```solidity
function _encode_sol_bytes7(bytes7 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes8

```solidity
function _encode_sol_bytes8(bytes8 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes9

```solidity
function _encode_sol_bytes9(bytes9 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes10

```solidity
function _encode_sol_bytes10(bytes10 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes11

```solidity
function _encode_sol_bytes11(bytes11 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes12

```solidity
function _encode_sol_bytes12(bytes12 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes13

```solidity
function _encode_sol_bytes13(bytes13 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes14

```solidity
function _encode_sol_bytes14(bytes14 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes15

```solidity
function _encode_sol_bytes15(bytes15 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes16

```solidity
function _encode_sol_bytes16(bytes16 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes17

```solidity
function _encode_sol_bytes17(bytes17 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes18

```solidity
function _encode_sol_bytes18(bytes18 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes19

```solidity
function _encode_sol_bytes19(bytes19 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes20

```solidity
function _encode_sol_bytes20(bytes20 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes21

```solidity
function _encode_sol_bytes21(bytes21 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes22

```solidity
function _encode_sol_bytes22(bytes22 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes23

```solidity
function _encode_sol_bytes23(bytes23 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes24

```solidity
function _encode_sol_bytes24(bytes24 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes25

```solidity
function _encode_sol_bytes25(bytes25 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes26

```solidity
function _encode_sol_bytes26(bytes26 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes27

```solidity
function _encode_sol_bytes27(bytes27 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes28

```solidity
function _encode_sol_bytes28(bytes28 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes29

```solidity
function _encode_sol_bytes29(bytes29 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes30

```solidity
function _encode_sol_bytes30(bytes30 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes31

```solidity
function _encode_sol_bytes31(bytes31 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_bytes32

```solidity
function _encode_sol_bytes32(bytes32 x, uint256 p, bytes bs) internal pure returns (uint256)
```

### _encode_sol_header

```solidity
function _encode_sol_header(uint256 sz, uint256 p, bytes bs) internal pure returns (uint256)
```

_Encode the key of Solidity integer and/or fixed-size bytes array._

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| sz | uint256 | The number of bytes used to encode Solidity types |
| p | uint256 | The offset of bytes array `bs` |
| bs | bytes | The bytes array to encode |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes used to encode |

### _encode_sol

```solidity
function _encode_sol(uint256 x, uint256 sz, uint256 p, bytes bs) internal pure returns (uint256)
```

_Encode Solidity type_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | uint256 | The unsinged integer to be encoded |
| sz | uint256 | The number of bytes used to encode Solidity types |
| p | uint256 | The offset of bytes array `bs` |
| bs | bytes | The bytes array to encode |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes used to encode |

### _encode_sol

```solidity
function _encode_sol(int256 x, uint256 sz, uint256 p, bytes bs) internal pure returns (uint256)
```

_Encode Solidity type_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | int256 | The signed integer to be encoded |
| sz | uint256 | The number of bytes used to encode Solidity types |
| p | uint256 | The offset of bytes array `bs` |
| bs | bytes | The bytes array to encode |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes used to encode |

### _encode_sol_bytes

```solidity
function _encode_sol_bytes(bytes32 x, uint256 sz, uint256 p, bytes bs) internal pure returns (uint256)
```

_Encode Solidity type_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | bytes32 | The fixed-size byte array to be encoded |
| sz | uint256 | The number of bytes used to encode Solidity types |
| p | uint256 | The offset of bytes array `bs` |
| bs | bytes | The bytes array to encode |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes used to encode |

### _get_real_size

```solidity
function _get_real_size(uint256 x, uint256 sz) internal pure returns (uint256)
```

_Get the actual size needed to encoding an unsigned integer_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | uint256 | The unsigned integer to be encoded |
| sz | uint256 | The maximum number of bytes used to encode Solidity types |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes needed for encoding `x` |

### _get_real_size

```solidity
function _get_real_size(int256 x, uint256 sz) internal pure returns (uint256)
```

_Get the actual size needed to encoding an signed integer_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | int256 | The signed integer to be encoded |
| sz | uint256 | The maximum number of bytes used to encode Solidity types |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes needed for encoding `x` |

### _encode_sol_raw_bytes_array

```solidity
function _encode_sol_raw_bytes_array(bytes32 x, uint256 p, bytes bs, uint256 sz) internal pure returns (uint256)
```

_Encode the fixed-bytes array_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | bytes32 | The fixed-size byte array to be encoded |
| p | uint256 | The offset of bytes array `bs` |
| bs | bytes | The bytes array to encode |
| sz | uint256 | The maximum number of bytes used to encode Solidity types |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes needed for encoding `x` |

### _encode_sol_raw_other

```solidity
function _encode_sol_raw_other(int256 x, uint256 p, bytes bs, uint256 sz) internal pure returns (uint256)
```

_Encode the signed integer_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | int256 | The signed integer to be encoded |
| p | uint256 | The offset of bytes array `bs` |
| bs | bytes | The bytes array to encode |
| sz | uint256 | The maximum number of bytes used to encode Solidity types |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes needed for encoding `x` |

### _encode_sol_raw_other

```solidity
function _encode_sol_raw_other(uint256 x, uint256 p, bytes bs, uint256 sz) internal pure returns (uint256)
```

_Encode the unsigned integer_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | uint256 | The unsigned integer to be encoded |
| p | uint256 | The offset of bytes array `bs` |
| bs | bytes | The bytes array to encode |
| sz | uint256 | The maximum number of bytes used to encode Solidity types |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes needed for encoding `x` |

