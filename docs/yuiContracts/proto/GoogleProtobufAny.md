# Solidity API

## GoogleProtobufAny

### Data

```solidity
struct Data {
  string type_url;
  bytes value;
}
```

### decode

```solidity
function decode(bytes bs) internal pure returns (struct GoogleProtobufAny.Data)
```

_The main decoder for memory_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct GoogleProtobufAny.Data | The decoded struct |

### decode

```solidity
function decode(struct GoogleProtobufAny.Data self, bytes bs) internal
```

_The main decoder for storage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| self | struct GoogleProtobufAny.Data | The in-storage struct |
| bs | bytes | The bytes array to be decoded |

### _decode

```solidity
function _decode(uint256 p, bytes bs, uint256 sz) internal pure returns (struct GoogleProtobufAny.Data, uint256)
```

_The decoder for internal usage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| sz | uint256 | The number of bytes expected |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct GoogleProtobufAny.Data | The decoded struct |
| [1] | uint256 | The number of bytes decoded |

### _read_type_url

```solidity
function _read_type_url(uint256 p, bytes bs, struct GoogleProtobufAny.Data r, uint256[3] counters) internal pure returns (uint256)
```

_The decoder for reading a field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| r | struct GoogleProtobufAny.Data | The in-memory struct |
| counters | uint256[3] | The counters for repeated fields |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes decoded |

### _read_value

```solidity
function _read_value(uint256 p, bytes bs, struct GoogleProtobufAny.Data r, uint256[3] counters) internal pure returns (uint256)
```

_The decoder for reading a field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| r | struct GoogleProtobufAny.Data | The in-memory struct |
| counters | uint256[3] | The counters for repeated fields |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes decoded |

### encode

```solidity
function encode(struct GoogleProtobufAny.Data r) internal pure returns (bytes)
```

_The main encoder for memory_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct GoogleProtobufAny.Data | The struct to be encoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes | The encoded byte array |

### _encode

```solidity
function _encode(struct GoogleProtobufAny.Data r, uint256 p, bytes bs) internal pure returns (uint256)
```

_The encoder for internal usage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct GoogleProtobufAny.Data | The struct to be encoded |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded |

### _encode_nested

```solidity
function _encode_nested(struct GoogleProtobufAny.Data r, uint256 p, bytes bs) internal pure returns (uint256)
```

_The encoder for inner struct_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct GoogleProtobufAny.Data | The struct to be encoded |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded |

### _estimate

```solidity
function _estimate(struct GoogleProtobufAny.Data r) internal pure returns (uint256)
```

_The estimator for a struct_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct GoogleProtobufAny.Data | The struct to be encoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded in estimation |

### store

```solidity
function store(struct GoogleProtobufAny.Data input, struct GoogleProtobufAny.Data output) internal
```

_Store in-memory struct to storage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| input | struct GoogleProtobufAny.Data | The in-memory struct |
| output | struct GoogleProtobufAny.Data | The in-storage struct |

### nil

```solidity
function nil() internal pure returns (struct GoogleProtobufAny.Data r)
```

_Return an empty struct_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct GoogleProtobufAny.Data | The empty struct |

### isNil

```solidity
function isNil(struct GoogleProtobufAny.Data x) internal pure returns (bool r)
```

_Test whether a struct is empty_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | struct GoogleProtobufAny.Data | The struct to be tested |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | bool | True if it is empty |

