# Solidity API

## IbcLightclientsMockV1ClientState

### Data

```solidity
struct Data {
  struct Height.Data latest_height;
}
```

### decode

```solidity
function decode(bytes bs) internal pure returns (struct IbcLightclientsMockV1ClientState.Data)
```

_The main decoder for memory_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct IbcLightclientsMockV1ClientState.Data | The decoded struct |

### decode

```solidity
function decode(struct IbcLightclientsMockV1ClientState.Data self, bytes bs) internal
```

_The main decoder for storage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| self | struct IbcLightclientsMockV1ClientState.Data | The in-storage struct |
| bs | bytes | The bytes array to be decoded |

### _decode

```solidity
function _decode(uint256 p, bytes bs, uint256 sz) internal pure returns (struct IbcLightclientsMockV1ClientState.Data, uint256)
```

_The decoder for internal usage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| sz | uint256 | The number of bytes expected |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct IbcLightclientsMockV1ClientState.Data | The decoded struct |
| [1] | uint256 | The number of bytes decoded |

### _read_latest_height

```solidity
function _read_latest_height(uint256 p, bytes bs, struct IbcLightclientsMockV1ClientState.Data r) internal pure returns (uint256)
```

_The decoder for reading a field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| r | struct IbcLightclientsMockV1ClientState.Data | The in-memory struct |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes decoded |

### _decode_Height

```solidity
function _decode_Height(uint256 p, bytes bs) internal pure returns (struct Height.Data, uint256)
```

_The decoder for reading a inner struct field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct Height.Data | The decoded inner-struct |
| [1] | uint256 | The number of bytes used to decode |

### encode

```solidity
function encode(struct IbcLightclientsMockV1ClientState.Data r) internal pure returns (bytes)
```

_The main encoder for memory_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct IbcLightclientsMockV1ClientState.Data | The struct to be encoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes | The encoded byte array |

### _encode

```solidity
function _encode(struct IbcLightclientsMockV1ClientState.Data r, uint256 p, bytes bs) internal pure returns (uint256)
```

_The encoder for internal usage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct IbcLightclientsMockV1ClientState.Data | The struct to be encoded |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded |

### _encode_nested

```solidity
function _encode_nested(struct IbcLightclientsMockV1ClientState.Data r, uint256 p, bytes bs) internal pure returns (uint256)
```

_The encoder for inner struct_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct IbcLightclientsMockV1ClientState.Data | The struct to be encoded |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded |

### _estimate

```solidity
function _estimate(struct IbcLightclientsMockV1ClientState.Data r) internal pure returns (uint256)
```

_The estimator for a struct_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct IbcLightclientsMockV1ClientState.Data | The struct to be encoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded in estimation |

### _empty

```solidity
function _empty(struct IbcLightclientsMockV1ClientState.Data r) internal pure returns (bool)
```

### store

```solidity
function store(struct IbcLightclientsMockV1ClientState.Data input, struct IbcLightclientsMockV1ClientState.Data output) internal
```

_Store in-memory struct to storage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| input | struct IbcLightclientsMockV1ClientState.Data | The in-memory struct |
| output | struct IbcLightclientsMockV1ClientState.Data | The in-storage struct |

### nil

```solidity
function nil() internal pure returns (struct IbcLightclientsMockV1ClientState.Data r)
```

_Return an empty struct_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct IbcLightclientsMockV1ClientState.Data | The empty struct |

### isNil

```solidity
function isNil(struct IbcLightclientsMockV1ClientState.Data x) internal pure returns (bool r)
```

_Test whether a struct is empty_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | struct IbcLightclientsMockV1ClientState.Data | The struct to be tested |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | bool | True if it is empty |

## IbcLightclientsMockV1ConsensusState

### Data

```solidity
struct Data {
  uint64 timestamp;
}
```

### decode

```solidity
function decode(bytes bs) internal pure returns (struct IbcLightclientsMockV1ConsensusState.Data)
```

_The main decoder for memory_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct IbcLightclientsMockV1ConsensusState.Data | The decoded struct |

### decode

```solidity
function decode(struct IbcLightclientsMockV1ConsensusState.Data self, bytes bs) internal
```

_The main decoder for storage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| self | struct IbcLightclientsMockV1ConsensusState.Data | The in-storage struct |
| bs | bytes | The bytes array to be decoded |

### _decode

```solidity
function _decode(uint256 p, bytes bs, uint256 sz) internal pure returns (struct IbcLightclientsMockV1ConsensusState.Data, uint256)
```

_The decoder for internal usage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| sz | uint256 | The number of bytes expected |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct IbcLightclientsMockV1ConsensusState.Data | The decoded struct |
| [1] | uint256 | The number of bytes decoded |

### _read_timestamp

```solidity
function _read_timestamp(uint256 p, bytes bs, struct IbcLightclientsMockV1ConsensusState.Data r) internal pure returns (uint256)
```

_The decoder for reading a field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| r | struct IbcLightclientsMockV1ConsensusState.Data | The in-memory struct |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes decoded |

### encode

```solidity
function encode(struct IbcLightclientsMockV1ConsensusState.Data r) internal pure returns (bytes)
```

_The main encoder for memory_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct IbcLightclientsMockV1ConsensusState.Data | The struct to be encoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes | The encoded byte array |

### _encode

```solidity
function _encode(struct IbcLightclientsMockV1ConsensusState.Data r, uint256 p, bytes bs) internal pure returns (uint256)
```

_The encoder for internal usage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct IbcLightclientsMockV1ConsensusState.Data | The struct to be encoded |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded |

### _encode_nested

```solidity
function _encode_nested(struct IbcLightclientsMockV1ConsensusState.Data r, uint256 p, bytes bs) internal pure returns (uint256)
```

_The encoder for inner struct_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct IbcLightclientsMockV1ConsensusState.Data | The struct to be encoded |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded |

### _estimate

```solidity
function _estimate(struct IbcLightclientsMockV1ConsensusState.Data r) internal pure returns (uint256)
```

_The estimator for a struct_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct IbcLightclientsMockV1ConsensusState.Data | The struct to be encoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded in estimation |

### _empty

```solidity
function _empty(struct IbcLightclientsMockV1ConsensusState.Data r) internal pure returns (bool)
```

### store

```solidity
function store(struct IbcLightclientsMockV1ConsensusState.Data input, struct IbcLightclientsMockV1ConsensusState.Data output) internal
```

_Store in-memory struct to storage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| input | struct IbcLightclientsMockV1ConsensusState.Data | The in-memory struct |
| output | struct IbcLightclientsMockV1ConsensusState.Data | The in-storage struct |

### nil

```solidity
function nil() internal pure returns (struct IbcLightclientsMockV1ConsensusState.Data r)
```

_Return an empty struct_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct IbcLightclientsMockV1ConsensusState.Data | The empty struct |

### isNil

```solidity
function isNil(struct IbcLightclientsMockV1ConsensusState.Data x) internal pure returns (bool r)
```

_Test whether a struct is empty_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | struct IbcLightclientsMockV1ConsensusState.Data | The struct to be tested |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | bool | True if it is empty |

## IbcLightclientsMockV1Header

### Data

```solidity
struct Data {
  struct Height.Data height;
  uint64 timestamp;
}
```

### decode

```solidity
function decode(bytes bs) internal pure returns (struct IbcLightclientsMockV1Header.Data)
```

_The main decoder for memory_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct IbcLightclientsMockV1Header.Data | The decoded struct |

### decode

```solidity
function decode(struct IbcLightclientsMockV1Header.Data self, bytes bs) internal
```

_The main decoder for storage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| self | struct IbcLightclientsMockV1Header.Data | The in-storage struct |
| bs | bytes | The bytes array to be decoded |

### _decode

```solidity
function _decode(uint256 p, bytes bs, uint256 sz) internal pure returns (struct IbcLightclientsMockV1Header.Data, uint256)
```

_The decoder for internal usage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| sz | uint256 | The number of bytes expected |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct IbcLightclientsMockV1Header.Data | The decoded struct |
| [1] | uint256 | The number of bytes decoded |

### _read_height

```solidity
function _read_height(uint256 p, bytes bs, struct IbcLightclientsMockV1Header.Data r) internal pure returns (uint256)
```

_The decoder for reading a field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| r | struct IbcLightclientsMockV1Header.Data | The in-memory struct |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes decoded |

### _read_timestamp

```solidity
function _read_timestamp(uint256 p, bytes bs, struct IbcLightclientsMockV1Header.Data r) internal pure returns (uint256)
```

_The decoder for reading a field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| r | struct IbcLightclientsMockV1Header.Data | The in-memory struct |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes decoded |

### _decode_Height

```solidity
function _decode_Height(uint256 p, bytes bs) internal pure returns (struct Height.Data, uint256)
```

_The decoder for reading a inner struct field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct Height.Data | The decoded inner-struct |
| [1] | uint256 | The number of bytes used to decode |

### encode

```solidity
function encode(struct IbcLightclientsMockV1Header.Data r) internal pure returns (bytes)
```

_The main encoder for memory_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct IbcLightclientsMockV1Header.Data | The struct to be encoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes | The encoded byte array |

### _encode

```solidity
function _encode(struct IbcLightclientsMockV1Header.Data r, uint256 p, bytes bs) internal pure returns (uint256)
```

_The encoder for internal usage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct IbcLightclientsMockV1Header.Data | The struct to be encoded |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded |

### _encode_nested

```solidity
function _encode_nested(struct IbcLightclientsMockV1Header.Data r, uint256 p, bytes bs) internal pure returns (uint256)
```

_The encoder for inner struct_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct IbcLightclientsMockV1Header.Data | The struct to be encoded |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded |

### _estimate

```solidity
function _estimate(struct IbcLightclientsMockV1Header.Data r) internal pure returns (uint256)
```

_The estimator for a struct_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct IbcLightclientsMockV1Header.Data | The struct to be encoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded in estimation |

### _empty

```solidity
function _empty(struct IbcLightclientsMockV1Header.Data r) internal pure returns (bool)
```

### store

```solidity
function store(struct IbcLightclientsMockV1Header.Data input, struct IbcLightclientsMockV1Header.Data output) internal
```

_Store in-memory struct to storage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| input | struct IbcLightclientsMockV1Header.Data | The in-memory struct |
| output | struct IbcLightclientsMockV1Header.Data | The in-storage struct |

### nil

```solidity
function nil() internal pure returns (struct IbcLightclientsMockV1Header.Data r)
```

_Return an empty struct_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct IbcLightclientsMockV1Header.Data | The empty struct |

### isNil

```solidity
function isNil(struct IbcLightclientsMockV1Header.Data x) internal pure returns (bool r)
```

_Test whether a struct is empty_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | struct IbcLightclientsMockV1Header.Data | The struct to be tested |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | bool | True if it is empty |

