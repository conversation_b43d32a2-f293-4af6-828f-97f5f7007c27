# Solidity API

## Height

### Data

```solidity
struct Data {
  uint64 revision_number;
  uint64 revision_height;
}
```

### decode

```solidity
function decode(bytes bs) internal pure returns (struct Height.Data)
```

_The main decoder for memory_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct Height.Data | The decoded struct |

### decode

```solidity
function decode(struct Height.Data self, bytes bs) internal
```

_The main decoder for storage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| self | struct Height.Data | The in-storage struct |
| bs | bytes | The bytes array to be decoded |

### _decode

```solidity
function _decode(uint256 p, bytes bs, uint256 sz) internal pure returns (struct Height.Data, uint256)
```

_The decoder for internal usage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| sz | uint256 | The number of bytes expected |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct Height.Data | The decoded struct |
| [1] | uint256 | The number of bytes decoded |

### _read_revision_number

```solidity
function _read_revision_number(uint256 p, bytes bs, struct Height.Data r) internal pure returns (uint256)
```

_The decoder for reading a field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| r | struct Height.Data | The in-memory struct |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes decoded |

### _read_revision_height

```solidity
function _read_revision_height(uint256 p, bytes bs, struct Height.Data r) internal pure returns (uint256)
```

_The decoder for reading a field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| r | struct Height.Data | The in-memory struct |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes decoded |

### encode

```solidity
function encode(struct Height.Data r) internal pure returns (bytes)
```

_The main encoder for memory_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct Height.Data | The struct to be encoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes | The encoded byte array |

### _encode

```solidity
function _encode(struct Height.Data r, uint256 p, bytes bs) internal pure returns (uint256)
```

_The encoder for internal usage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct Height.Data | The struct to be encoded |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded |

### _encode_nested

```solidity
function _encode_nested(struct Height.Data r, uint256 p, bytes bs) internal pure returns (uint256)
```

_The encoder for inner struct_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct Height.Data | The struct to be encoded |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded |

### _estimate

```solidity
function _estimate(struct Height.Data r) internal pure returns (uint256)
```

_The estimator for a struct_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct Height.Data | The struct to be encoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded in estimation |

### _empty

```solidity
function _empty(struct Height.Data r) internal pure returns (bool)
```

### store

```solidity
function store(struct Height.Data input, struct Height.Data output) internal
```

_Store in-memory struct to storage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| input | struct Height.Data | The in-memory struct |
| output | struct Height.Data | The in-storage struct |

### nil

```solidity
function nil() internal pure returns (struct Height.Data r)
```

_Return an empty struct_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct Height.Data | The empty struct |

### isNil

```solidity
function isNil(struct Height.Data x) internal pure returns (bool r)
```

_Test whether a struct is empty_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | struct Height.Data | The struct to be tested |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | bool | True if it is empty |

