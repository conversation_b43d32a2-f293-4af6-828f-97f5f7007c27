# Solidity API

## ConnectionEnd

### State

```solidity
enum State {
  STATE_UNINITIALIZED_UNSPECIFIED,
  STATE_INIT,
  STATE_TRYOPEN,
  STATE_OPEN
}
```

### encode_State

```solidity
function encode_State(enum ConnectionEnd.State x) internal pure returns (int32)
```

### decode_State

```solidity
function decode_State(int64 x) internal pure returns (enum ConnectionEnd.State)
```

### estimate_packed_repeated_State

```solidity
function estimate_packed_repeated_State(enum ConnectionEnd.State[] a) internal pure returns (uint256)
```

_The estimator for an packed enum array_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded |

### Data

```solidity
struct Data {
  string client_id;
  struct Version.Data[] versions;
  enum ConnectionEnd.State state;
  struct Counterparty.Data counterparty;
  uint64 delay_period;
}
```

### decode

```solidity
function decode(bytes bs) internal pure returns (struct ConnectionEnd.Data)
```

_The main decoder for memory_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct ConnectionEnd.Data | The decoded struct |

### decode

```solidity
function decode(struct ConnectionEnd.Data self, bytes bs) internal
```

_The main decoder for storage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| self | struct ConnectionEnd.Data | The in-storage struct |
| bs | bytes | The bytes array to be decoded |

### _decode

```solidity
function _decode(uint256 p, bytes bs, uint256 sz) internal pure returns (struct ConnectionEnd.Data, uint256)
```

_The decoder for internal usage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| sz | uint256 | The number of bytes expected |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct ConnectionEnd.Data | The decoded struct |
| [1] | uint256 | The number of bytes decoded |

### _read_client_id

```solidity
function _read_client_id(uint256 p, bytes bs, struct ConnectionEnd.Data r) internal pure returns (uint256)
```

_The decoder for reading a field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| r | struct ConnectionEnd.Data | The in-memory struct |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes decoded |

### _read_unpacked_repeated_versions

```solidity
function _read_unpacked_repeated_versions(uint256 p, bytes bs, struct ConnectionEnd.Data r, uint256[6] counters) internal pure returns (uint256)
```

_The decoder for reading a field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| r | struct ConnectionEnd.Data | The in-memory struct |
| counters | uint256[6] | The counters for repeated fields |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes decoded |

### _read_state

```solidity
function _read_state(uint256 p, bytes bs, struct ConnectionEnd.Data r) internal pure returns (uint256)
```

_The decoder for reading a field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| r | struct ConnectionEnd.Data | The in-memory struct |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes decoded |

### _read_counterparty

```solidity
function _read_counterparty(uint256 p, bytes bs, struct ConnectionEnd.Data r) internal pure returns (uint256)
```

_The decoder for reading a field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| r | struct ConnectionEnd.Data | The in-memory struct |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes decoded |

### _read_delay_period

```solidity
function _read_delay_period(uint256 p, bytes bs, struct ConnectionEnd.Data r) internal pure returns (uint256)
```

_The decoder for reading a field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| r | struct ConnectionEnd.Data | The in-memory struct |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes decoded |

### _decode_Version

```solidity
function _decode_Version(uint256 p, bytes bs) internal pure returns (struct Version.Data, uint256)
```

_The decoder for reading a inner struct field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct Version.Data | The decoded inner-struct |
| [1] | uint256 | The number of bytes used to decode |

### _decode_Counterparty

```solidity
function _decode_Counterparty(uint256 p, bytes bs) internal pure returns (struct Counterparty.Data, uint256)
```

_The decoder for reading a inner struct field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct Counterparty.Data | The decoded inner-struct |
| [1] | uint256 | The number of bytes used to decode |

### encode

```solidity
function encode(struct ConnectionEnd.Data r) internal pure returns (bytes)
```

_The main encoder for memory_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct ConnectionEnd.Data | The struct to be encoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes | The encoded byte array |

### _encode

```solidity
function _encode(struct ConnectionEnd.Data r, uint256 p, bytes bs) internal pure returns (uint256)
```

_The encoder for internal usage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct ConnectionEnd.Data | The struct to be encoded |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded |

### _encode_nested

```solidity
function _encode_nested(struct ConnectionEnd.Data r, uint256 p, bytes bs) internal pure returns (uint256)
```

_The encoder for inner struct_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct ConnectionEnd.Data | The struct to be encoded |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded |

### _estimate

```solidity
function _estimate(struct ConnectionEnd.Data r) internal pure returns (uint256)
```

_The estimator for a struct_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct ConnectionEnd.Data | The struct to be encoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded in estimation |

### _empty

```solidity
function _empty(struct ConnectionEnd.Data r) internal pure returns (bool)
```

### store

```solidity
function store(struct ConnectionEnd.Data input, struct ConnectionEnd.Data output) internal
```

_Store in-memory struct to storage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| input | struct ConnectionEnd.Data | The in-memory struct |
| output | struct ConnectionEnd.Data | The in-storage struct |

### addVersions

```solidity
function addVersions(struct ConnectionEnd.Data self, struct Version.Data value) internal pure
```

_Add value to an array_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| self | struct ConnectionEnd.Data | The in-memory struct |
| value | struct Version.Data | The value to add |

### nil

```solidity
function nil() internal pure returns (struct ConnectionEnd.Data r)
```

_Return an empty struct_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct ConnectionEnd.Data | The empty struct |

### isNil

```solidity
function isNil(struct ConnectionEnd.Data x) internal pure returns (bool r)
```

_Test whether a struct is empty_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | struct ConnectionEnd.Data | The struct to be tested |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | bool | True if it is empty |

## Counterparty

### Data

```solidity
struct Data {
  string client_id;
  string connection_id;
  struct MerklePrefix.Data prefix;
}
```

### decode

```solidity
function decode(bytes bs) internal pure returns (struct Counterparty.Data)
```

_The main decoder for memory_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct Counterparty.Data | The decoded struct |

### decode

```solidity
function decode(struct Counterparty.Data self, bytes bs) internal
```

_The main decoder for storage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| self | struct Counterparty.Data | The in-storage struct |
| bs | bytes | The bytes array to be decoded |

### _decode

```solidity
function _decode(uint256 p, bytes bs, uint256 sz) internal pure returns (struct Counterparty.Data, uint256)
```

_The decoder for internal usage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| sz | uint256 | The number of bytes expected |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct Counterparty.Data | The decoded struct |
| [1] | uint256 | The number of bytes decoded |

### _read_client_id

```solidity
function _read_client_id(uint256 p, bytes bs, struct Counterparty.Data r) internal pure returns (uint256)
```

_The decoder for reading a field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| r | struct Counterparty.Data | The in-memory struct |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes decoded |

### _read_connection_id

```solidity
function _read_connection_id(uint256 p, bytes bs, struct Counterparty.Data r) internal pure returns (uint256)
```

_The decoder for reading a field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| r | struct Counterparty.Data | The in-memory struct |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes decoded |

### _read_prefix

```solidity
function _read_prefix(uint256 p, bytes bs, struct Counterparty.Data r) internal pure returns (uint256)
```

_The decoder for reading a field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| r | struct Counterparty.Data | The in-memory struct |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes decoded |

### _decode_MerklePrefix

```solidity
function _decode_MerklePrefix(uint256 p, bytes bs) internal pure returns (struct MerklePrefix.Data, uint256)
```

_The decoder for reading a inner struct field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct MerklePrefix.Data | The decoded inner-struct |
| [1] | uint256 | The number of bytes used to decode |

### encode

```solidity
function encode(struct Counterparty.Data r) internal pure returns (bytes)
```

_The main encoder for memory_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct Counterparty.Data | The struct to be encoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes | The encoded byte array |

### _encode

```solidity
function _encode(struct Counterparty.Data r, uint256 p, bytes bs) internal pure returns (uint256)
```

_The encoder for internal usage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct Counterparty.Data | The struct to be encoded |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded |

### _encode_nested

```solidity
function _encode_nested(struct Counterparty.Data r, uint256 p, bytes bs) internal pure returns (uint256)
```

_The encoder for inner struct_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct Counterparty.Data | The struct to be encoded |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded |

### _estimate

```solidity
function _estimate(struct Counterparty.Data r) internal pure returns (uint256)
```

_The estimator for a struct_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct Counterparty.Data | The struct to be encoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded in estimation |

### _empty

```solidity
function _empty(struct Counterparty.Data r) internal pure returns (bool)
```

### store

```solidity
function store(struct Counterparty.Data input, struct Counterparty.Data output) internal
```

_Store in-memory struct to storage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| input | struct Counterparty.Data | The in-memory struct |
| output | struct Counterparty.Data | The in-storage struct |

### nil

```solidity
function nil() internal pure returns (struct Counterparty.Data r)
```

_Return an empty struct_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct Counterparty.Data | The empty struct |

### isNil

```solidity
function isNil(struct Counterparty.Data x) internal pure returns (bool r)
```

_Test whether a struct is empty_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | struct Counterparty.Data | The struct to be tested |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | bool | True if it is empty |

## Version

### Data

```solidity
struct Data {
  string identifier;
  string[] features;
}
```

### decode

```solidity
function decode(bytes bs) internal pure returns (struct Version.Data)
```

_The main decoder for memory_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct Version.Data | The decoded struct |

### decode

```solidity
function decode(struct Version.Data self, bytes bs) internal
```

_The main decoder for storage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| self | struct Version.Data | The in-storage struct |
| bs | bytes | The bytes array to be decoded |

### _decode

```solidity
function _decode(uint256 p, bytes bs, uint256 sz) internal pure returns (struct Version.Data, uint256)
```

_The decoder for internal usage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| sz | uint256 | The number of bytes expected |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct Version.Data | The decoded struct |
| [1] | uint256 | The number of bytes decoded |

### _read_identifier

```solidity
function _read_identifier(uint256 p, bytes bs, struct Version.Data r) internal pure returns (uint256)
```

_The decoder for reading a field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| r | struct Version.Data | The in-memory struct |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes decoded |

### _read_unpacked_repeated_features

```solidity
function _read_unpacked_repeated_features(uint256 p, bytes bs, struct Version.Data r, uint256[3] counters) internal pure returns (uint256)
```

_The decoder for reading a field_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |
| r | struct Version.Data | The in-memory struct |
| counters | uint256[3] | The counters for repeated fields |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes decoded |

### encode

```solidity
function encode(struct Version.Data r) internal pure returns (bytes)
```

_The main encoder for memory_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct Version.Data | The struct to be encoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes | The encoded byte array |

### _encode

```solidity
function _encode(struct Version.Data r, uint256 p, bytes bs) internal pure returns (uint256)
```

_The encoder for internal usage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct Version.Data | The struct to be encoded |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded |

### _encode_nested

```solidity
function _encode_nested(struct Version.Data r, uint256 p, bytes bs) internal pure returns (uint256)
```

_The encoder for inner struct_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct Version.Data | The struct to be encoded |
| p | uint256 | The offset of bytes array to start decode |
| bs | bytes | The bytes array to be decoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded |

### _estimate

```solidity
function _estimate(struct Version.Data r) internal pure returns (uint256)
```

_The estimator for a struct_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct Version.Data | The struct to be encoded |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | The number of bytes encoded in estimation |

### _empty

```solidity
function _empty(struct Version.Data r) internal pure returns (bool)
```

### store

```solidity
function store(struct Version.Data input, struct Version.Data output) internal
```

_Store in-memory struct to storage_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| input | struct Version.Data | The in-memory struct |
| output | struct Version.Data | The in-storage struct |

### addFeatures

```solidity
function addFeatures(struct Version.Data self, string value) internal pure
```

_Add value to an array_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| self | struct Version.Data | The in-memory struct |
| value | string | The value to add |

### nil

```solidity
function nil() internal pure returns (struct Version.Data r)
```

_Return an empty struct_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | struct Version.Data | The empty struct |

### isNil

```solidity
function isNil(struct Version.Data x) internal pure returns (bool r)
```

_Test whether a struct is empty_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| x | struct Version.Data | The struct to be tested |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | bool | True if it is empty |

