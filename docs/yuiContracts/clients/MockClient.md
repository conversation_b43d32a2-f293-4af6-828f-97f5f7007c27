# Solidity API

## MockClient

### ib<PERSON><PERSON><PERSON><PERSON>

```solidity
address ibc<PERSON><PERSON><PERSON>
```

### clientStates

```solidity
mapping(string => struct IbcLightclientsMockV1ClientState.Data) clientStates
```

### consensusStates

```solidity
mapping(string => mapping(uint128 => struct IbcLightclientsMockV1ConsensusState.Data)) consensusStates
```

### statuses

```solidity
mapping(string => enum ClientStatus) statuses
```

### constructor

```solidity
constructor(address ibcHandler_) public
```

### createClient

```solidity
function createClient(string clientId, bytes clientStateBytes, bytes consensusStateBytes) external virtual returns (bytes32 clientStateCommitment, struct ConsensusStateUpdate update, bool ok)
```

_createClient creates a new client with the given state_

### getTimestampAtHeight

```solidity
function getTimestampAtHeight(string clientId, struct Height.Data height) external view virtual returns (uint64, bool)
```

_getTimestampAtHeight returns the timestamp of the consensus state at the given height.
     The timestamp is nanoseconds since unix epoch._

### getLatestHeight

```solidity
function getLatestHeight(string clientId) external view virtual returns (struct Height.Data, bool)
```

_getLatestHeight returns the latest height of the client state corresponding to `clientId`._

### getStatus

```solidity
function getStatus(string clientId) external view virtual returns (enum ClientStatus)
```

_getStatus returns the status of the client corresponding to `clientId`._

### updateClient

```solidity
function updateClient(string clientId, bytes clientMessageBytes) external virtual returns (bytes32 clientStateCommitment, struct ConsensusStateUpdate[] updates, bool ok)
```

_updateClient is intended to perform the followings:
1. verify a given client message(e.g. header)
2. check misbehaviour such like duplicate block height
3. if misbehaviour is found, update state accordingly and return
4. update state(s) with the client message
5. persist the state(s) on the host_

### verifyMembership

```solidity
function verifyMembership(string clientId, struct Height.Data height, uint64, uint64, bytes proof, bytes prefix, bytes, bytes value) external view virtual returns (bool)
```

_verifyMembership is a generic proof verification method which verifies a proof of the existence of a value at a given CommitmentPath at the specified height.
The caller is expected to construct the full CommitmentPath from a CommitmentPrefix and a standardized path (as defined in ICS 24)._

### verifyNonMembership

```solidity
function verifyNonMembership(string clientId, struct Height.Data height, uint64, uint64, bytes proof, bytes prefix, bytes) external view virtual returns (bool)
```

_verifyNonMembership is a generic proof verification method which verifies the absence of a given CommitmentPath at a specified height.
The caller is expected to construct the full CommitmentPath from a CommitmentPrefix and a standardized path (as defined in ICS 24)._

### getClientState

```solidity
function getClientState(string clientId) external view virtual returns (bytes clientStateBytes, bool)
```

_getClientState returns the clientState corresponding to `clientId`.
     If it's not found, the function returns false._

### getConsensusState

```solidity
function getConsensusState(string clientId, struct Height.Data height) external view virtual returns (bytes consensusStateBytes, bool)
```

_getConsensusState returns the consensusState corresponding to `clientId` and `height`.
     If it's not found, the function returns false._

### parseHeader

```solidity
function parseHeader(bytes bz) internal pure returns (struct Height.Data, uint64)
```

### unmarshalClientState

```solidity
function unmarshalClientState(bytes bz) internal pure returns (struct IbcLightclientsMockV1ClientState.Data clientState, bool ok)
```

### unmarshalConsensusState

```solidity
function unmarshalConsensusState(bytes bz) internal pure returns (struct IbcLightclientsMockV1ConsensusState.Data consensusState, bool ok)
```

### onlyIBC

```solidity
modifier onlyIBC()
```

