# Solidity API

## IBCAppBase

_Base contract of the IBC App protocol_

### onlyIBC

```solidity
modifier onlyIBC()
```

_Throws if called by any account other than the IBC contract._

### ibcAddress

```solidity
function ibcAddress() public view virtual returns (address)
```

_Returns the address of the IBC contract._

### _checkIBC

```solidity
function _checkIBC() internal view virtual
```

_Throws if the sender is not the IBC contract._

### onChanOpenInit

```solidity
function onChanOpenInit(struct IIBCModule.MsgOnChanOpenInit) external virtual returns (string)
```

_See IIBCModule-onChanOpenInit

NOTE: You should apply an `onlyIBC` modifier to the function if a derived contract overrides it._

### onChanOpenTry

```solidity
function onChanOpenTry(struct IIBCModule.MsgOnChanOpenTry) external virtual returns (string)
```

_See IIBCModule-onChanOpenTry

NOTE: You should apply an `onlyIBC` modifier to the function if a derived contract overrides it._

### onChanOpenAck

```solidity
function onChanOpenAck(struct IIBCModule.MsgOnChanOpenAck) external virtual
```

_See IIBCModule-onChanOpenAck

NOTE: You should apply an `onlyIBC` modifier to the function if a derived contract overrides it._

### onChanOpenConfirm

```solidity
function onChanOpenConfirm(struct IIBCModule.MsgOnChanOpenConfirm) external virtual
```

_See IIBCModule-onChanOpenConfirm

NOTE: You should apply an `onlyIBC` modifier to the function if a derived contract overrides it._

### onChanCloseInit

```solidity
function onChanCloseInit(struct IIBCModule.MsgOnChanCloseInit) external virtual
```

_See IIBCModule-onChanCloseInit

NOTE: You should apply an `onlyIBC` modifier to the function if a derived contract overrides it._

### onChanCloseConfirm

```solidity
function onChanCloseConfirm(struct IIBCModule.MsgOnChanCloseConfirm) external virtual
```

_See IIBCModule-onChanCloseConfirm

NOTE: You should apply an `onlyIBC` modifier to the function if a derived contract overrides it._

### onRecvPacket

```solidity
function onRecvPacket(struct Packet.Data, address) external virtual returns (bytes acknowledgement)
```

_See IIBCModule-onRecvPacket

NOTE: You should apply an `onlyIBC` modifier to the function if a derived contract overrides it._

### onAcknowledgementPacket

```solidity
function onAcknowledgementPacket(struct Packet.Data, bytes, address) external virtual
```

_See IIBCModule-onAcknowledgementPacket

NOTE: You should apply an `onlyIBC` modifier to the function if a derived contract overrides it._

### onTimeoutPacket

```solidity
function onTimeoutPacket(struct Packet.Data, address relayer) external virtual
```

_See IIBCModule-onTimeoutPacket

NOTE: You should apply an `onlyIBC` modifier to the function if a derived contract overrides it._

