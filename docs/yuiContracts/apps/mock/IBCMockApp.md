# Solidity API

## IBCMockApp

### MOCKAPP_VERSION

```solidity
string MOCKAPP_VERSION
```

### ibc<PERSON>and<PERSON>

```solidity
contract IIBCHandler ibcHandler
```

### constructor

```solidity
constructor(contract IIBCHandler ibcHandler_) public
```

### ibcAddress

```solidity
function ibcAddress() public view virtual returns (address)
```

_Returns the address of the IBC contract._

### sendPacket

```solidity
function sendPacket(bytes message, string sourcePort, string sourceChannel, struct Height.Data timeoutHeight, uint64 timeoutTimestamp) external returns (uint64)
```

### onRecvPacket

```solidity
function onRecvPacket(struct Packet.Data packet, address) external returns (bytes acknowledgement)
```

### onAcknowledgementPacket

```solidity
function onAcknowledgementPacket(struct Packet.Data packet, bytes acknowledgement, address) external virtual
```

### onChanOpenInit

```solidity
function onChanOpenInit(struct IIBCModule.MsgOnChanOpenInit msg_) external virtual returns (string)
```

### onChanOpenTry

```solidity
function onChanOpenTry(struct IIBCModule.MsgOnChanOpenTry msg_) external virtual returns (string)
```

