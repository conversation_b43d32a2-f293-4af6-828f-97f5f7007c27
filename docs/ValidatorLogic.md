# Solidity API

## ValidatorLogic

_ValidatorLogicコントラクト
     外部公開用コントラクト_

### _adminOnly

```solidity
function _adminOnly(bytes32 hash, uint256 deadline, bytes signature) internal view
```

_Admin権限を持つかチェックする。署名からEOAを復元し権限を持つかチェックする(内部関数)。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| hash | bytes32 | signatureの計算元ハッシュ値 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 権限チェック対象の署名 |

### initialize

```solidity
function initialize(contract IContractManager contractManager, contract IValidatorStorage validatorStorage) public
```

_初期化関数_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerアドレス |
| validatorStorage | contract IValidatorStorage | ValidatorStorageアドレス |

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | コントラクトバージョン |

### addValidator

```solidity
function addValidator(bytes32 validatorId, bytes32 issuerId, bytes32 name, bytes32 traceId, uint256 deadline, bytes signature) external
```

_Validatorの追加。Adminの権限が必要。

```
emit event: AddValidator()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| issuerId | bytes32 | issuerId |
| name | bytes32 | validator名 |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### addValidatorRole

```solidity
function addValidatorRole(bytes32 validatorId, address validatorEoa, bytes32 traceId, uint256 deadline, bytes signature) external
```

_Validator権限の追加。Adminの権限が必要。

```
emit event: AddValidatorRole()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| validatorEoa | address | validatorEoa |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### modValidator

```solidity
function modValidator(bytes32 validatorId, bytes32 name, bytes32 traceId, uint256 deadline, bytes signature) external
```

_検証者の名前を更新する。Adminの権限が必要。

```
emit event: ModValidator()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| name | bytes32 | validator名 |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### modAccount

```solidity
function modAccount(bytes32 validatorId, bytes32 accountId, string accountName, bytes32 traceId, uint256 deadline, bytes signature) external
```

_アカウントの名前を更新する。バリデータの権限が必要。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | アカウントID |
| accountName | string | アカウント名 |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | バリデータによる署名 |

### setTerminated

```solidity
function setTerminated(bytes32 validatorId, bytes32 accountId, bytes32 reasonCode, bytes32 traceId) external
```

_Accountを解約する。

```
emit event: SetTerminated()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |
| reasonCode | bytes32 | 理由コード |
| traceId | bytes32 | トレースID |

### addAccount

```solidity
function addAccount(bytes32 validatorId, bytes32 accountId, string accountName, struct AccountLimitValues limitValues, bytes32 traceId) external
```

_Accountを登録する。

```
emit event: AddAccount()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |
| accountName | string | account名 |
| limitValues | struct AccountLimitValues | アカウントの限度額値 |
| traceId | bytes32 | トレースID |

### addValidatorAccountId

```solidity
function addValidatorAccountId(bytes32 validatorId, bytes32 accountId, bytes32 traceId) external
```

_バリデータが直接管理するアカウントIDを追加する

```
emit event: AddValidatorAccountId()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | バリデータID |
| accountId | bytes32 | アカウントID |
| traceId | bytes32 | トレースID |

### setActiveBusinessAccountWithZone

```solidity
function setActiveBusinessAccountWithZone(bytes32 validatorId, uint16 zoneId, bytes32 accountId, bytes32 traceId) external
```

_BusinessZoneAccountを追加する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| zoneId | uint16 | ゾーンID |
| accountId | bytes32 | アカウントID |
| traceId | bytes32 | トレースID |

### setBizZoneTerminated

```solidity
function setBizZoneTerminated(uint16 zoneId, bytes32 accountId, bytes32 traceId) external
```

_BusinessZoneアカウント解約

```
emit event: SetBizZoneTerminated()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | ゾーンID |
| accountId | bytes32 | アカウントID |
| traceId | bytes32 | トレースID |

### syncAccount

```solidity
function syncAccount(bytes32 validatorId, bytes32 accountId, string accountName, uint16 zoneId, string zoneName, bytes32 accountStatus, bytes32 reasonCode, uint256 approvalAmount, bytes32 traceId) external
```

_BizZone向けAccount登録。(CoreAPI/synchronous用)

```
emit event: SyncAccount()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |
| accountName | string | accountName |
| zoneId | uint16 | zoneId |
| zoneName | string | zoneName |
| accountStatus | bytes32 | 口座のステータス |
| reasonCode | bytes32 | 理由コード |
| approvalAmount | uint256 | 承認額 |
| traceId | bytes32 | トレースID |

### setValidatorAll

```solidity
function setValidatorAll(struct ValidatorAll validator, uint256 deadline, bytes signature) external
```

_指定されたValidatorIdに紐づくValidator情報を登録、もしくは上書きする
     バックアップリスト作業時のみ実行_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validator | struct ValidatorAll | validator |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### hasValidator

```solidity
function hasValidator(bytes32 validatorId) external view returns (bool success, string err)
```

_検証者IDが登録済であるか確認する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:登録済,false:未登録 |
| err | string | エラーメッセージ |

### getValidatorList

```solidity
function getValidatorList(uint256 limit, uint256 offset) external view returns (struct ValidatorListData[] validators, uint256 totalCount, string err)
```

_検証者情報リストを取得する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| limit | uint256 | limit |
| offset | uint256 | offset |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| validators | struct ValidatorListData[] | validators |
| totalCount | uint256 | validatorの数 |
| err | string | エラーメッセージ |

### getValidator

```solidity
function getValidator(bytes32 validatorId) external view returns (bytes32 name, bytes32 issuerId, string err)
```

_Validatorの情報を取得する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| name | bytes32 | validatorの名前 |
| issuerId | bytes32 | validatorに紐づくissuerId |
| err | string | エラーメッセージ |

### getValidatorCount

```solidity
function getValidatorCount() external view returns (uint256)
```

_Validatorの総数をカウントする。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | count Validatorの総数 |

### hasAccount

```solidity
function hasAccount(bytes32 validatorId, bytes32 accountId) external view returns (bool success, string err)
```

_指定されたValidatorIDにAccountが紐付いているか確認を行う。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:紐づいている,false:紐づいていない |
| err | string | エラーメッセージ |

### hasValidatorByAccount

```solidity
function hasValidatorByAccount(bytes32 validatorId, bytes32 accountId) external view returns (bool)
```

_バリデータ権限を持っているか確認する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bool | success true:権限あり,false:権限なし |

### getAccount

```solidity
function getAccount(bytes32 validatorId, bytes32 accountId) external view returns (struct AccountDataWithLimitData accountData, string err)
```

_Validatorに紐づくAccountの情報を取得する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountData | struct AccountDataWithLimitData | アカウントデータ(zoneIdなし) |
| err | string | エラーメッセージ |

### getValidatorAccountId

```solidity
function getValidatorAccountId(bytes32 validatorId) external view returns (bytes32 accountId, string err)
```

_バリデータが直接管理するアカウントIDを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | バリデータID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| err | string | エラー |

### getValidatorId

```solidity
function getValidatorId(uint256 index) external view returns (bytes32 validatorId, string err)
```

_Indexに応じたValidatorIdを返す。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| index | uint256 | index |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| err | string | エラーメッセージ |

### getAccountAllList

```solidity
function getAccountAllList(bytes32 validatorId, uint256 offset, uint256 limit) external view returns (struct ValidatorAccountsDataALL[] accounts, uint256 totalCount, string err)
```

_(CoreBatch専用 BCClient経由)該当ValidatorIDに紐づくAccountの情報を取得する。
レスポンスのValidatorAccountsDataのソート順について
validatorMapping[validatorId].accountIdsの格納順の逆順となる。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| offset | uint256 | オフセット |
| limit | uint256 | リミット |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accounts | struct ValidatorAccountsDataALL[] | ValidatorAccountsData[] |
| totalCount | uint256 |  |
| err | string | エラーメッセージ |

### getAccountList

```solidity
function getAccountList(bytes32 validatorId, uint256 offset, uint256 limit, string sortOrder) external view returns (struct ValidatorAccountsData[] accounts, uint256 totalCount, string err)
```

_該当ValidatorIDに紐づくAccountの情報を取得する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| offset | uint256 | オフセット |
| limit | uint256 | リミット |
| sortOrder | string | ソート順(desc: 降順, asc: 昇順) |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accounts | struct ValidatorAccountsData[] | ValidatorAccountsData[] |
| totalCount | uint256 |  |
| err | string | エラーメッセージ |

### getZoneByAccountId

```solidity
function getZoneByAccountId(bytes32 validatorId, bytes32 accountId) external view returns (struct ZoneData[] zones, string err)
```

_アカウントに連携済みのzone情報一覧を取得する。(finZone含む)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| zones | struct ZoneData[] | zoneIdの配列 |
| err | string | エラーメッセージ |

### hasValidatorRole

```solidity
function hasValidatorRole(bytes32 validatorId, bytes32 hash, uint256 deadline, bytes signature) external view returns (bool success, string err)
```

_バリデータ権限を持っているか確認する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| hash | bytes32 | hash |
| deadline | uint256 | deadline |
| signature | bytes | signature |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:権限あり,false:権限なし |
| err | string | エラーメッセージ |

### getValidatorAll

```solidity
function getValidatorAll(uint256 index) external view returns (struct ValidatorAll)
```

_バックアップ用に検証者データを取得する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| index | uint256 | index |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct ValidatorAll | validator ValidatorAll構造体 |

### getDestinationAccount

```solidity
function getDestinationAccount(bytes32 accountId) external view returns (string accountName, string err)
```

_移転先のアカウント情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountName | string | アカウント名 |
| err | string | エラー |

### getAccountAll

```solidity
function getAccountAll(bytes32 validatorId, bytes32 accountId) external view returns (struct AccountDataAll accountDataAll, string err)
```

_Validatorに紐づくAccountの全情報を取得する。(接続済みBusinessZoneAccount情報含む)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataAll | struct AccountDataAll | アカウントデータ(zoneIdあり) |
| err | string | エラーメッセージ |

