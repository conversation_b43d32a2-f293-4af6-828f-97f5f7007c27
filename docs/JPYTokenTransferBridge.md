# Solidity API

## JPYTokenTransferBridge

### finZoneId

```solidity
uint16 finZoneId
```

### tokenTransferSourcePort

```solidity
string tokenTransferSourcePort
```

### tokenTransferSourceChannel

```solidity
string tokenTransferSourceChannel
```

### tokenTransferSourceVersion

```solidity
string tokenTransferSourceVersion
```

### adminOnly

```solidity
modifier adminOnly(uint256 deadline, bytes signature)
```

### initialize

```solidity
function initialize(contract IIBCHandler ibcHandler_, address ibcTokenAddr, address accessCtrlAddr) public
```

_Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| ibcHandler_ | contract IIBCHandler | ibcHandler |
| ibcTokenAddr | address | ibcToken |
| accessCtrlAddr | address | accessCtrl |

### version

```solidity
function version() external pure virtual returns (string)
```

_コントラクトバージョン取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | version コントラクトバージョン |

### ibcAddress

```solidity
function ibcAddress() public view virtual returns (address)
```

_Returns the address of the IBC contract._

### setAddress

```solidity
function setAddress(contract IIBCToken ibcTokenAddr, contract IAccessCtrl accessCtrlAddr, uint256 deadline, bytes signature) public
```

### setChannel

```solidity
function setChannel(uint16 zoneId, string channel, uint256 deadline, bytes signature) public
```

### getConfig

```solidity
function getConfig() external view returns (struct Config config)
```

### registerEscrowAccount

```solidity
function registerEscrowAccount(uint16 srcZoneId, uint16 dstZoneId, bytes32 eAccount, uint256 deadline, bytes signature) external
```

### unregisterEscrowAccount

```solidity
function unregisterEscrowAccount(uint16 srcZoneId, uint16 dstZoneId, uint256 deadline, bytes signature) external
```

### escrowAccount

```solidity
function escrowAccount(uint16 srcZoneId, uint16 dstZoneId) external view returns (bytes32)
```

### transfer

```solidity
function transfer(bytes32 accountId, uint16 fromZoneId, uint16 toZoneId, uint256 amount, uint64 timeoutHeight, bytes32 traceId) external returns (uint256)
```

### discharge

```solidity
function discharge(bytes32 accountId, uint16 fromZoneId, uint16 toZoneId, uint256 amount, uint64 timeoutHeight, bytes32 traceId) external returns (uint256)
```

_Fin起点のディスチャージを行うため、対抗チェーンへpacketの送信を行う関数_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | 送信元のアカウントID |
| fromZoneId | uint16 | ディスチャージ元のゾーンID |
| toZoneId | uint16 | ディスチャージ先のゾーンID |
| amount | uint256 | ディスチャージする金額 |
| timeoutHeight | uint64 | packetがタイムアウトするブロック高 |
| traceId | bytes32 | トレースID |

### onRecvPacket

```solidity
function onRecvPacket(struct Packet.Data packet, address) external returns (bytes acknowledgement)
```

_relayerからpacketを受け取り、Financial Zoneで管理するBusiness Zoneのステータスを更新する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| packet | struct Packet.Data | packet |
|  | address |  |

### onChanOpenInit

```solidity
function onChanOpenInit(struct IIBCModule.MsgOnChanOpenInit msg_) external view returns (string)
```

### onChanOpenTry

```solidity
function onChanOpenTry(struct IIBCModule.MsgOnChanOpenTry msg_) external view returns (string)
```

### recoverPacket

```solidity
function recoverPacket(struct Packet.Data packet, address, uint256 deadline, bytes signature) external
```

_リカバリーのためADMIN権限を利用してpacket receiveを再実行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| packet | struct Packet.Data | packet |
|  | address |  |
| deadline | uint256 |  |
| signature | bytes |  |

