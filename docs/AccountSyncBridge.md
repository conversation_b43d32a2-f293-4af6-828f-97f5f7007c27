# Solidity API

## AccountSyncBridge

### finZoneId

```solidity
uint16 finZoneId
```

### accountSyncSourcePort

```solidity
string accountSyncSourcePort
```

### accountSyncSourceChannel

```solidity
string accountSyncSourceChannel
```

### accountSyncSourceVersion

```solidity
string accountSyncSourceVersion
```

### adminOnly

```solidity
modifier adminOnly(uint256 deadline, bytes signature)
```

### initialize

```solidity
function initialize(contract IIBCHandler ibcHandler_, address providerAddr, address validatorAddr, address accessCtrlAddr, address businessZoneAccountAddr, address ibcTokenAddr) public
```

_Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| ibcHandler_ | contract IIBCHandler | ibcH<PERSON><PERSON> |
| providerAddr | address | providerAddr |
| validatorAddr | address | validator |
| accessCtrlAddr | address | accessCtrl |
| businessZoneAccountAddr | address | businessZoneAccount |
| ibcTokenAddr | address | ibcToken |

### version

```solidity
function version() external pure virtual returns (string)
```

_コントラクトバージョン取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | version コントラクトバージョン |

### ibcAddress

```solidity
function ibcAddress() public view virtual returns (address)
```

_Returns the address of the IBC contract._

### setAddress

```solidity
function setAddress(contract IProvider providerAddr, contract IValidator validatorAddr, contract IAccessCtrl accessCtrlAddr, contract IBusinessZoneAccount businessZoneAccountAddr, contract IIBCToken ibcTokenAddr, uint256 deadline, bytes signature) public
```

### setChannel

```solidity
function setChannel(uint16 zoneId, string channel, uint256 deadline, bytes signature) public
```

### getConfig

```solidity
function getConfig() external view returns (struct Config config)
```

### getPort

```solidity
function getPort() external view returns (string port)
```

### syncAccount

```solidity
function syncAccount(bytes32 validatorId, bytes32 accountId, string accountName, uint16 fromZoneId, string zoneName, bytes32 accountStatus, bytes32 reasonCode, uint256 approvalAmount, bytes32 traceId, uint64 timeoutHeight) external returns (uint256)
```

### onRecvPacket

```solidity
function onRecvPacket(struct Packet.Data packet, address) external returns (bytes)
```

_relayerからpacketを受け取り、Financial Zoneで管理するBusiness Zoneのステータスを更新する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| packet | struct Packet.Data | packet |
|  | address |  |

### onAcknowledgementPacket

```solidity
function onAcknowledgementPacket(struct Packet.Data packet, bytes, address) external virtual
```

_packet受け取り後の処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| packet | struct Packet.Data | packet |
|  | bytes |  |
|  | address |  |

### onChanOpenInit

```solidity
function onChanOpenInit(struct IIBCModule.MsgOnChanOpenInit msg_) external view returns (string)
```

### onChanOpenTry

```solidity
function onChanOpenTry(struct IIBCModule.MsgOnChanOpenTry msg_) external view returns (string)
```

### recoverPacket

```solidity
function recoverPacket(struct Packet.Data packet, address, uint256 deadline, bytes signature) external
```

_リカバリーのためADMIN権限を利用してpacket receiveを再実行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| packet | struct Packet.Data | packet |
|  | address |  |
| deadline | uint256 |  |
| signature | bytes |  |

