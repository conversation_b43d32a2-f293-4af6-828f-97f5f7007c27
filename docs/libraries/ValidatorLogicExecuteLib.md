# Solidity API

## ValidatorLogicExecuteLib

_ValidatorLogicExecuteLibライブラリ
     Validatorの実行関数を実装するヘルパーライブラリ_

### executeSyncAccount

```solidity
function executeSyncAccount(contract IValidatorStorage validatorStorage, contract IContractManager contractManager, bytes32 validatorId, bytes32 accountId, string accountName, bytes32 accountStatus, bytes32 reasonCode, uint256 approvalAmount, bytes32 traceId) external
```

_BizZone向けAccount登録処理（同期処理）_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| contractManager | contract IContractManager | ContractManager参照 |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |
| accountName | string | account名 |
| accountStatus | bytes32 | 口座のステータス |
| reasonCode | bytes32 | 理由コード |
| approvalAmount | uint256 | 承認額 |
| traceId | bytes32 | トレースID |

### executeAddValidatorRole

```solidity
function executeAddValidatorRole(contract IValidatorStorage validatorStorage, contract IContractManager contractManager, bytes32 validatorId, address validatorEoa) external
```

_Validator権限の追加処理（アクセス制御含む）_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| contractManager | contract IContractManager | ContractManager参照 |
| validatorId | bytes32 | validatorId |
| validatorEoa | address | validatorEoaアドレス |

### executeAddAccountWithLimits

```solidity
function executeAddAccountWithLimits(contract IValidatorStorage validatorStorage, contract IContractManager contractManager, bytes32 validatorId, bytes32 accountId, string accountName, struct AccountLimitValues limitValues, bytes32 traceId) external
```

_Accountを限度額付きで登録する（共通領域用）_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| contractManager | contract IContractManager | ContractManager参照 |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |
| accountName | string | account名 |
| limitValues | struct AccountLimitValues | アカウントの限度額値 |
| traceId | bytes32 | トレースID |

### executeAddValidator

```solidity
function executeAddValidator(contract IValidatorStorage validatorStorage, bytes32 validatorId, bytes32 issuerId, bytes32 name) external
```

_Validatorの追加処理（検証とセットアップ）_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| validatorId | bytes32 | validatorId |
| issuerId | bytes32 | issuerId |
| name | bytes32 | validator名 |

### updateValidatorName

```solidity
function updateValidatorName(contract IValidatorStorage validatorStorage, bytes32 validatorId, bytes32 name) external
```

_バリデータ名を更新_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| validatorId | bytes32 | バリデータID |
| name | bytes32 | 新しい名前 |

### updateValidatorAccountId

```solidity
function updateValidatorAccountId(contract IValidatorStorage validatorStorage, bytes32 validatorId, bytes32 validatorAccountId) external
```

_バリデータアカウントIDを更新_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| validatorId | bytes32 | バリデータID |
| validatorAccountId | bytes32 | バリデータアカウントID |

### setValidatorAll

```solidity
function setValidatorAll(contract IValidatorStorage validatorStorage, struct ValidatorAll validator, uint256 deadline, bytes signature) external
```

_バックアップ用に全バリデータデータを設定_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| validator | struct ValidatorAll | 全バリデータデータ |
| deadline | uint256 | 署名期限 |
| signature | bytes | 署名 |

