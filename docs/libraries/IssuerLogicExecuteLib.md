# Solidity API

## IssuerLogicExecuteLib

_IssuerLogicExecuteLibライブラリ
     Issuerの実行関数を実装するヘルパーライブラリ_

### ROLE_PREFIX_ISSUER

```solidity
bytes32 ROLE_PREFIX_ISSUER
```

_Issuerロール計算用(calcRole()のprefix用文字列(Issuer権限))_

### executeAddIssuer

```solidity
function executeAddIssuer(contract IIssuerStorage issuerStorage, contract IContractManager contractManager, bytes32 issuerId, uint16 bankCode, string name) external
```

_Issuerの追加処理を実行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerStorage | contract IIssuerStorage | IssuerStorage参照 |
| contractManager | contract IContractManager | ContractManager参照 |
| issuerId | bytes32 | issuerId |
| bankCode | uint16 | 金融機関コード |
| name | string | issuer名 |

### executeAddAccountId

```solidity
function executeAddAccountId(contract IIssuerStorage issuerStorage, bytes32 issuerId, bytes32 accountId) external
```

_issuerにaccountを紐付ける処理を実行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerStorage | contract IIssuerStorage | IssuerStorage参照 |
| issuerId | bytes32 | issuerId |
| accountId | bytes32 | accountId |

### updateIssuerName

```solidity
function updateIssuerName(contract IIssuerStorage issuerStorage, bytes32 issuerId, string name) external
```

_issuer名の更新処理を実行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerStorage | contract IIssuerStorage | IssuerStorage参照 |
| issuerId | bytes32 | issuerId |
| name | string | issuer名 |

### setIssuerAll

```solidity
function setIssuerAll(contract IIssuerStorage issuerStorage, struct IssuerAll issuer, uint256 deadline, bytes signature) external
```

_バックアップ用に全発行者データを設定する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerStorage | contract IIssuerStorage | IssuerStorage参照 |
| issuer | struct IssuerAll | 全発行者データ |
| deadline | uint256 | 署名の期限 |
| signature | bytes | Admin署名 |

