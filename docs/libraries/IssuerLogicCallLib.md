# Solidity API

## IssuerLogicCallLib

_IssuerLogicCallLibライブラリ
     Issuerのview/pure関数を実装するヘルパーライブラリ
     ブロックチェーンデータの変更は行わない_

### checkIssuerIdIsValid

```solidity
function checkIssuerIdIsValid(bytes32 issuerId) external pure returns (bool success, string err)
```

_issuerIdの入力検証_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | チェック対象となる発行者ID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:有効,false:無効 |
| err | string | エラーメッセージ |

### checkAddIssuerRoleIsValid

```solidity
function checkAddIssuerRoleIsValid(contract IIssuerStorage issuerStorage, bytes32 issuerId, address issuerEoa) external view
```

_IssuerRole追加の有効性チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |
| issuerId | bytes32 | 発行者ID |
| issuerEoa | address | 発行者EOA |

### checkAddIssuerIsValid

```solidity
function checkAddIssuerIsValid(contract IIssuerStorage issuerStorage, uint16 bankCode) external view
```

_AddIssuer時のbankCode重複チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |
| bankCode | uint16 | 金融機関コード |

### checkHasIssuer

```solidity
function checkHasIssuer(contract IIssuerStorage issuerStorage, bytes32 issuerId) external view
```

_issuerの存在チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |
| issuerId | bytes32 | 発行者ID |

### checkIssuerHasAccount

```solidity
function checkIssuerHasAccount(contract IIssuerStorage issuerStorage, bytes32 issuerId, bytes32 accountId) public view
```

_issuerに紐づくaccountの存在チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |
| issuerId | bytes32 | 発行者ID |
| accountId | bytes32 | アカウントID |

### checkModTokenLimitIsValid

```solidity
function checkModTokenLimitIsValid(contract IContractManager contractManager, contract IIssuerStorage issuerStorage, bytes32 issuerId, bytes32 accountId) external view
```

_アカウント限度額更新の検証処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |
| issuerId | bytes32 | 発行者ID |
| accountId | bytes32 | アカウントID |

### checkCumulativeResetIsValid

```solidity
function checkCumulativeResetIsValid(contract IContractManager contractManager, contract IIssuerStorage issuerStorage, bytes32 issuerId, bytes32 accountId) external view
```

_累積限度額初期化の検証処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |
| issuerId | bytes32 | 発行者ID |
| accountId | bytes32 | アカウントID |

### checkAccountNotTerminated

```solidity
function checkAccountNotTerminated(contract IContractManager contractManager, bytes32 accountId) public view
```

_アカウント終了チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| accountId | bytes32 | アカウントID |

### checkAddAccountIdIsValid

```solidity
function checkAddAccountIdIsValid(contract IContractManager contractManager, contract IIssuerStorage issuerStorage, address sender, bytes32 issuerId, bytes32 accountId) external view
```

_addAccountIdの検証処理（統合版）_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |
| sender | address | メッセージ送信者 |
| issuerId | bytes32 | 発行者ID |
| accountId | bytes32 | アカウントID |

### checkMintIsValid

```solidity
function checkMintIsValid(contract IContractManager contractManager, contract IIssuerStorage issuerStorage, bytes32 issuerId, bytes32 accountId, uint256 amount, uint256 deadline, bytes signature) external view returns (bool success, string err)
```

_Mint前の事前チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |
| issuerId | bytes32 | 発行者ID |
| accountId | bytes32 | アカウントID |
| amount | uint256 | Mint数量 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | チェック結果 |
| err | string | エラーメッセージ |

### checkBurnIsValid

```solidity
function checkBurnIsValid(contract IContractManager contractManager, contract IIssuerStorage issuerStorage, bytes32 issuerId, bytes32 accountId, uint256 amount, uint256 deadline, bytes signature) external view returns (bool success, string err)
```

_Burn前の事前チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |
| issuerId | bytes32 | 発行者ID |
| accountId | bytes32 | アカウントID |
| amount | uint256 | Burn数量 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | チェック結果 |
| err | string | エラーメッセージ |

### hasIssuer

```solidity
function hasIssuer(contract IIssuerStorage issuerStorage, bytes32 issuerId) public view returns (bool success, string err)
```

_発行者の存在確認_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |
| issuerId | bytes32 | チェック対象となる発行者Id |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:存在する,false:存在しない |
| err | string | エラーメッセージ |

### hasAccount

```solidity
function hasAccount(contract IIssuerStorage issuerStorage, bytes32 issuerId, bytes32 accountId) public view returns (bool success, string err)
```

_指定されたIssuerIDにAccountが紐付いているか確認を行う(内部関数)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |
| issuerId | bytes32 | issuerId |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:紐づいている,false:紐づいていない |
| err | string | エラーメッセージ |

### hasAdminRole

```solidity
function hasAdminRole(contract IContractManager contractManager, bytes32 hash, uint256 deadline, bytes signature) external view returns (bool has, string err)
```

_管理者権限チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| hash | bytes32 | signatureの計算元ハッシュ値 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| has | bool | true:権限あり,false:権限なし |
| err | string | エラーメッセージ |

### hasIssuerRole

```solidity
function hasIssuerRole(contract IContractManager contractManager, contract IIssuerStorage issuerStorage, bytes32 issuerId, bytes32 hash, uint256 deadline, bytes signature) external view returns (bool has, string err)
```

_権限チェック。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |
| issuerId | bytes32 | issuerId |
| hash | bytes32 | signatureの計算元ハッシュ値 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| has | bool | true:権限あり,false:権限なし |
| err | string | エラーメッセージ |

### getIssuerData

```solidity
function getIssuerData(contract IIssuerStorage issuerStorage, bytes32 issuerId) external view returns (struct IssuerData issuerData)
```

_発行者データを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |
| issuerId | bytes32 | 発行者ID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerData | struct IssuerData | 発行者データ |

### getIssuerIdsCount

```solidity
function getIssuerIdsCount(contract IIssuerStorage issuerStorage) external view returns (uint256)
```

_発行者IDの総数を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | count 発行者IDの総数 |

### getIssuerIdByIndex

```solidity
function getIssuerIdByIndex(contract IIssuerStorage issuerStorage, uint256 index) external view returns (bytes32)
```

_発行者IDを指定インデックスで取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |
| index | uint256 | インデックス |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes32 | issuerId 発行者ID |

### getIssuerAll

```solidity
function getIssuerAll(contract IIssuerStorage issuerStorage, uint256 index) external view returns (struct IssuerAll)
```

_バックアップ用に発行者データを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |
| index | uint256 | インデックス |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct IssuerAll | issuer 全発行者データ |

### getIssuerList

```solidity
function getIssuerList(contract IIssuerStorage issuerStorage, uint256 limit, uint256 offset) external view returns (struct IssuerListData[] issuers, uint256 totalCount, string err)
```

_発行者情報リストを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |
| limit | uint256 | limit |
| offset | uint256 | offset |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuers | struct IssuerListData[] | issuers |
| totalCount | uint256 | issuerの数 |
| err | string | エラーメッセージ |

### getAccountList

```solidity
function getAccountList(contract IIssuerStorage issuerStorage, contract IContractManager contractManager, bytes32 issuerId, bytes32[] inAccountIds, uint256 limit, uint256 offset) external view returns (struct IssuerAccountsData[] accounts, uint256 totalCount, string err)
```

_該当IssuerIDに紐づくAccountIDを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| issuerId | bytes32 | issuerId |
| inAccountIds | bytes32[] | inAccountIds |
| limit | uint256 | limit |
| offset | uint256 | offset |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accounts | struct IssuerAccountsData[] | 指定のアカウントを取得する |
| totalCount | uint256 | アカウントの総数 |
| err | string | エラーメッセージ |

### getAccount

```solidity
function getAccount(contract IIssuerStorage issuerStorage, contract IContractManager contractManager, bytes32 issuerId, bytes32 accountId) external view returns (string accountName, uint256 balance, bytes32 accountStatus, bytes32 reasonCode, string err)
```

_発行者に紐づくアカウント情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerStorage | contract IIssuerStorage | IssuerStorageコントラクト参照 |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| issuerId | bytes32 | 発行者ID |
| accountId | bytes32 | アカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountName | string | アカウント名 |
| balance | uint256 | 残高 |
| accountStatus | bytes32 | アカウントステータス |
| reasonCode | bytes32 | 理由コード |
| err | string | エラーメッセージ |

### isAccountFrozen

```solidity
function isAccountFrozen(contract IContractManager contractManager, bytes32 accountId) external view returns (bool frozen, string err)
```

_アカウントの凍結状態を確認する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| accountId | bytes32 | アカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| frozen | bool | 凍結状態 |
| err | string | エラーメッセージ |

