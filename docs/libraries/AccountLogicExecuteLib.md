# Solidity API

## AccountLogicExecuteLib

_AccountLogicExecuteLibライブラリ
     Accountの実行関数を実装するヘルパーライブラリ_

### executeAddAccount

```solidity
function executeAddAccount(contract IAccountStorage accountStorage, bytes32 accountId, string accountName, bytes32 validatorId) external
```

_Accountの追加処理を実行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorageコントラクト参照 |
| accountId | bytes32 |  |
| accountName | string | account名 |
| validatorId | bytes32 |  |

### executeAddValidatorId

```solidity
function executeAddValidatorId(contract IAccountStorage accountStorage, bytes32 validatorId, bytes32 accountId) external
```

_validatorIdにaccountを紐付ける処理を実行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorageコントラクト参照 |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |

### updateAccountName

```solidity
function updateAccountName(contract IAccountStorage accountStorage, bytes32 accountId, string accountName) external
```

_アカウント名の変更_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage |  |
| accountId | bytes32 | accountId |
| accountName | string | アカウント名 |

### setAccountStatusAndReasonCode

```solidity
function setAccountStatusAndReasonCode(contract IAccountStorage accountStorage, bytes32 accountId, bytes32 accountStatus, bytes32 reasonCode) external
```

_Accountの有効性を更新する(凍結 or アクティブ)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage |  |
| accountId | bytes32 | マッピングのキーとなるアカウントID |
| accountStatus | bytes32 | アカウントステータス |
| reasonCode | bytes32 | 理由コード |

### setApprove

```solidity
function setApprove(contract IAccountStorage accountStorage, bytes32 ownerId, bytes32 spenderId, uint256 approvedAt, uint256 amount) external
```

_送金許可設定。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage |  |
| ownerId | bytes32 | ownerId |
| spenderId | bytes32 | spenderId |
| approvedAt | uint256 | 支払い許可日時 |
| amount | uint256 | 許容額 |

### setTerminated

```solidity
function setTerminated(contract IAccountStorage accountStorage, bytes32 accountId, bytes32 reasonCode) external
```

_アカウントのステータスを解約済みに更新する　TODO:他関数と統合する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorageコントラクト参照 |
| accountId | bytes32 | マッピングのキーとなるアカウントID |
| reasonCode | bytes32 | 理由コード |

### addZone

```solidity
function addZone(contract IAccountStorage accountStorage, bytes32 accountId, uint16 zoneId) external
```

_連携済みzone情報の追加_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorageコントラクト参照 |
| accountId | bytes32 | マッピングのキーとなるアカウントID |
| zoneId | uint16 | zoneId |

### setBalance

```solidity
function setBalance(contract IAccountStorage accountStorage, bytes32 accountId, uint256 amount, bool isAddition) external returns (uint256 balance)
```

_残高編集_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage |  |
| accountId | bytes32 | マッピングのキーとなるアカウントID |
| amount | uint256 | 発行額 |
| isAddition | bool | true:加算 / false:減産 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| balance | uint256 | 発行後の残高 |

### forceBurn

```solidity
function forceBurn(contract IAccountStorage accountStorage, contract IContractManager contractManager, bytes32 accountId) external returns (uint256 burnedAmount, uint256 burnedBalance, struct ForceDischarge[] forceDischarge)
```

_アカウントを強制償却させる_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage |  |
| contractManager | contract IContractManager |  |
| accountId | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| burnedAmount | uint256 | 償却額 |
| burnedBalance | uint256 |  |
| forceDischarge | struct ForceDischarge[] |  |

### partialForceBurn

```solidity
function partialForceBurn(contract IContractManager contractManager, contract IAccountStorage accountStorage, bytes32 accountId, uint256 burnedAmount, uint256 burnedBalance) external returns (struct ForceDischarge[] forceDischarge)
```

_アカウントを部分的に強制償却させる_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManager リファレンス |
| accountStorage | contract IAccountStorage | AccountStorage 契約リファレンス |
| accountId | bytes32 | マッピングのキーとなるアカウントID |
| burnedAmount | uint256 | 償却する金額 |
| burnedBalance | uint256 | 償却後に残す金額 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| forceDischarge | struct ForceDischarge[] | ディスチャージしたBizゾーン情報 |

### setAllowance

```solidity
function setAllowance(contract IAccountStorage accountStorage, bytes32 ownerId, bytes32 spenderId, uint256 amount) external
```

_送金許可額の減額を行う。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorage 契約リファレンス |
| ownerId | bytes32 | ownerId |
| spenderId | bytes32 | spenderId |
| amount | uint256 | 送金額 |

### setAccountAll

```solidity
function setAccountAll(contract IAccountStorage accountStorage, struct AccountsAll account) external
```

_指定されたAccountIdに紐づくAccount情報を登録、もしくは上書きする
     バックアップリスト作業時のみ実行_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorage 契約リファレンス |
| account | struct AccountsAll | 全発行者データ |

