# Solidity API

## TokenLogicCallLib

_TokenLogicCallLibライブラリ
     Tokenのview/pure関数を実装するヘルパーライブラリ
     ブロックチェーンデータの変更は行わない_

### checkAccountTermination

```solidity
function checkAccountTermination(contract IContractManager contractManager, bytes32 accountId) public view
```

_アカウント解約状況の確認_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManager参照 |
| accountId | bytes32 | アカウントID |

### getTokenIsValid

```solidity
function getTokenIsValid(contract IContractManager contractManager, address sender) external view
```

_Token取得時の検証処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManager参照 |
| sender | address | 送信者アドレス |

### addTokenIsValid

```solidity
function addTokenIsValid(contract IContractManager contractManager, contract ITokenStorage tokenStorage, bytes32 tokenId, address sender) external view
```

_Token追加時の検証処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManager参照 |
| tokenStorage | contract ITokenStorage | TokenStorage参照 |
| tokenId | bytes32 | トークンID |
| sender | address | 送信者アドレス |

### modTokenIsValid

```solidity
function modTokenIsValid(contract IContractManager contractManager, contract ITokenStorage tokenStorage, bytes32 tokenId, address sender) external view
```

_Token修正時の検証処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManager参照 |
| tokenStorage | contract ITokenStorage | TokenStorage参照 |
| tokenId | bytes32 | トークンID |
| sender | address | 送信者アドレス |

### setTokenEnabledIsValid

```solidity
function setTokenEnabledIsValid(contract IContractManager contractManager, contract ITokenStorage tokenStorage, bytes32 providerId, bytes32 tokenId, bool enabled, uint256 deadline, bytes signature) external view
```

_Token有効性設定時の検証処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManager参照 |
| tokenStorage | contract ITokenStorage | TokenStorage参照 |
| providerId | bytes32 | プロバイダーID |
| tokenId | bytes32 | トークンID |
| enabled | bool | 有効性フラグ |
| deadline | uint256 | 署名期限 |
| signature | bytes | 署名 |

### hasValidator

```solidity
function hasValidator(contract IContractManager contractManager, bytes32 validatorId) external view
```

_Validator存在確認_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManager参照 |
| validatorId | bytes32 | バリデーターID |

### mintIsValid

```solidity
function mintIsValid(bytes32 issuerId, bytes32 accountId) external pure
```

_Mint処理の検証_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | 発行者ID |
| accountId | bytes32 | アカウントID |

### burnIsValid

```solidity
function burnIsValid(bytes32 issuerId, bytes32 accountId) external pure
```

_Burn処理の検証_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | 発行者ID |
| accountId | bytes32 | アカウントID |

### burnCancelIsValid

```solidity
function burnCancelIsValid(contract IContractManager contractManager, contract ITokenStorage tokenStorage, struct BurnCancelData burnCancelData) external view
```

_償却取り消し処理の検証_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManager参照 |
| tokenStorage | contract ITokenStorage | TokenStorage参照 |
| burnCancelData | struct BurnCancelData | 償却取り消しデータ |

### getToken

```solidity
function getToken(contract ITokenStorage tokenStorage, bytes32 tokenId) public view returns (bytes32 name, bytes32 symbol, uint256 totalSupply, bool enabled, string err)
```

_Token情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract ITokenStorage | TokenStorage参照 |
| tokenId | bytes32 | トークンID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| name | bytes32 | トークンIDに紐づくトークンの名 |
| symbol | bytes32 | トークンIDに紐づくトークンのsymbol |
| totalSupply | uint256 | トークンIDに紐づくトークンの総供給量 |
| enabled | bool | トークンIDに紐づくture:有効,false:無効 |
| err | string | エラーメッセージ |

### getBalanceList

```solidity
function getBalanceList(contract IContractManager contractManager, bytes32 accountId) external view returns (uint16[] zoneIds, string[] zoneNames, uint256[] balances, string[] accountNames, bytes32[] accountStatus, uint256 totalBalance, string err)
```

_ビジネスゾーンの全残高情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManager参照 |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneIds | uint16[] | ビジネスゾーンID |
| zoneNames | string[] | ゾーン名 |
| balances | uint256[] | 残高 |
| accountNames | string[] | アカウント名 |
| accountStatus | bytes32[] | アカウントステータス |
| totalBalance | uint256 | 合計残高 |
| err | string |  |

### checkApprove

```solidity
function checkApprove(contract IContractManager contractManager, bytes32 validatorId, bytes32 ownerId, bytes32 spenderId, uint256 amount, bytes accountSignature, bytes info, uint256 deadline, bytes signature) external view returns (bool success, string err)
```

_送金許可の事前チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManager参照 |
| validatorId | bytes32 | バリデーターID |
| ownerId | bytes32 | オーナーID |
| spenderId | bytes32 | スペンダーID |
| amount | uint256 | 許可額 |
| accountSignature | bytes | アカウント署名 |
| info | bytes | 署名情報 |
| deadline | uint256 | 期限 |
| signature | bytes | バリデーター署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | 成功フラグ |
| err | string | エラーメッセージ |

### hasToken

```solidity
function hasToken(contract ITokenStorage tokenStorage, bytes32 tokenId, bytes32 chkTokenId, bool chkEnabled) public view returns (bool success, string err)
```

_Token存在確認_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract ITokenStorage | TokenStorage参照 |
| tokenId | bytes32 | 確認するトークンID |
| chkTokenId | bytes32 | チェック対象のトークンID |
| chkEnabled | bool | 有効性確認フラグ |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | 存在フラグ |
| err | string | エラーメッセージ |

### getToken

```solidity
function getToken(contract ITokenStorage tokenStorage) external view returns (bytes32 tokenId, bytes32 name, bytes32 symbol, uint256 totalSupply, bool enabled, string err)
```

_Token情報の取得（Provider権限チェック付き）_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract ITokenStorage | TokenStorage参照 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | tokenId |
| name | bytes32 | token名 |
| symbol | bytes32 | symbol |
| totalSupply | uint256 | tokenの総供給量 |
| enabled | bool | ture:有効,false:無効 |
| err | string | エラーメッセージ |

### getTokenAll

```solidity
function getTokenAll(contract ITokenStorage tokenStorage) external view returns (struct TokenAll token)
```

_Token全情報取得
     既に登録されているTokenの全情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract ITokenStorage | TokenStorage参照 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| token | struct TokenAll | 全Tokenの情報 |

### getTokenId

```solidity
function getTokenId(contract ITokenStorage tokenStorage) external view returns (bytes32 tokenId)
```

_TokenId取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract ITokenStorage | TokenStorage参照 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | TokenId |

### getAllowance

```solidity
function getAllowance(contract IContractManager contractManager, bytes32 validatorId, bytes32 ownerId, bytes32 spenderId) external view returns (uint256 allowance, uint256 approvedAt, string err)
```

_許可額情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManager参照 |
| validatorId | bytes32 | バリデーターID |
| ownerId | bytes32 | オーナーID |
| spenderId | bytes32 | スペンダーID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| allowance | uint256 | 許可額 |
| approvedAt | uint256 | 許可日時 |
| err | string | エラーメッセージ |

