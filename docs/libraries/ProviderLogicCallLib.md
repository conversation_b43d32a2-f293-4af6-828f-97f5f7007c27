# Solidity API

## ProviderLogicCallLib

_ProviderLogicCallLibライブラリ
     Providerのview/pure関数を実装するヘルパーライブラリ
     ブロックチェーンデータの変更は行わない_

### checkAddProviderRoleIsValid

```solidity
function checkAddProviderRoleIsValid(contract IProviderStorage providerStorage, bytes32 providerId, address providerEoa) external view
```

_ProviderRole追加の有効性チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerStorage | contract IProviderStorage | ProviderStorageコントラクト参照 |
| providerId | bytes32 | プロバイダID |
| providerEoa | address | プロバイダEOA |

### checkAddProviderIsValid

```solidity
function checkAddProviderIsValid(contract IProviderStorage providerStorage, bytes32 providerId) external view
```

_AddProvider時のproviderId重複チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerStorage | contract IProviderStorage | ProviderStorageコントラクト参照 |
| providerId | bytes32 | プロバイダID |

### validateProviderExists

```solidity
function validateProviderExists(contract IProviderStorage providerStorage) internal view
```

_Provider存在チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerStorage | contract IProviderStorage | ProviderStorageコントラクト参照 |

### getProviderInfo

```solidity
function getProviderInfo(contract IProviderStorage providerStorage) internal view returns (bytes32 providerId, uint16 zoneId, string zoneName, string err)
```

_Provider情報取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerStorage | contract IProviderStorage | ProviderStorageコントラクト参照 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerId | bytes32 | プロバイダーID |
| zoneId | uint16 | ゾーンID |
| zoneName | string | ゾーン名 |
| err | string | エラーメッセージ |

### checkHasProvider

```solidity
function checkHasProvider(contract IProviderStorage providerStorage, bytes32 providerId) external view
```

_providerの存在チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerStorage | contract IProviderStorage | ProviderStorageコントラクト参照 |
| providerId | bytes32 | プロバイダID |

### checkAddTokenIsValid

```solidity
function checkAddTokenIsValid(contract IContractManager contractManager, contract IProviderStorage providerStorage, bytes32 providerId, bytes32 tokenId, uint256 deadline, bytes signature, bytes32 name, bytes32 symbol) external view
```

_addTokenの検証処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| providerStorage | contract IProviderStorage | ProviderStorageコントラクト参照 |
| providerId | bytes32 | プロバイダID |
| tokenId | bytes32 | トークンID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 署名 |
| name | bytes32 |  |
| symbol | bytes32 |  |

### checkModTokenIsValid

```solidity
function checkModTokenIsValid(contract IContractManager contractManager, contract IProviderStorage providerStorage, bytes32 tokenId, uint256 deadline, bytes signature, bytes32 name, bytes32 symbol) external view
```

_modTokenの検証処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| providerStorage | contract IProviderStorage | ProviderStorageコントラクト参照 |
| tokenId | bytes32 | トークンID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 署名 |
| name | bytes32 |  |
| symbol | bytes32 |  |

### checkAddBizZoneToIssuerIsValid

```solidity
function checkAddBizZoneToIssuerIsValid(contract IProviderStorage providerStorage, bytes32 issuerId, uint16 zoneId) external view
```

_addBizZoneToIssuerの検証処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerStorage | contract IProviderStorage | ProviderStorageコントラクト参照 |
| issuerId | bytes32 | 発行者ID |
| zoneId | uint16 | ゾーンID |

### checkDeleteBizZoneToIssuerIsValid

```solidity
function checkDeleteBizZoneToIssuerIsValid(contract IProviderStorage providerStorage, uint16 zoneId) external view
```

_deleteBizZoneToIssuerの検証処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerStorage | contract IProviderStorage | ProviderStorageコントラクト参照 |
| zoneId | uint16 | ゾーンID |

### hasProvider

```solidity
function hasProvider(contract IProviderStorage providerStorage, bytes32 providerId) public view returns (bool success, string err)
```

_プロバイダの存在確認_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerStorage | contract IProviderStorage | ProviderStorageコントラクト参照 |
| providerId | bytes32 | チェック対象となるプロバイダID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:存在する,false:存在しない |
| err | string | エラーメッセージ |

### hasToken

```solidity
function hasToken(contract IProviderStorage providerStorage, contract IContractManager contractManager, bytes32 tokenId, bytes32 providerId, bool chkEnabled) public view returns (bool success, string err)
```

_トークンの存在確認_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerStorage | contract IProviderStorage | ProviderStorageコントラクト参照 |
| contractManager | contract IContractManager |  |
| tokenId | bytes32 | チェック対象となるトークンID |
| providerId | bytes32 | チェック対象となるプロバイダID |
| chkEnabled | bool | true:有効性確認を行う,false:有効性確認を行わない |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:トークンが存在し有効,false:トークンが存在しないまたは無効 |
| err | string | エラーメッセージ |

### hasZone

```solidity
function hasZone(contract IProviderStorage providerStorage, bytes32 providerId, uint16 zoneId) public view returns (bool success, string err)
```

_指定されたProviderIDにZoneが紐付いているか確認を行う_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerStorage | contract IProviderStorage | ProviderStorageコントラクト参照 |
| providerId | bytes32 | プロバイダID |
| zoneId | uint16 | ゾーンID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:紐づいている,false:紐づいていない |
| err | string | エラーメッセージ |

### hasAdminRole

```solidity
function hasAdminRole(contract IContractManager contractManager, bytes32 hash, uint256 deadline, bytes signature) external view returns (bool has, string err)
```

_管理者権限チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| hash | bytes32 | signatureの計算元ハッシュ値 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| has | bool | true:権限あり,false:権限なし |
| err | string | エラーメッセージ |

### hasProviderRole

```solidity
function hasProviderRole(contract IContractManager contractManager, contract IProviderStorage providerStorage, bytes32 providerId, bytes32 hash, uint256 deadline, bytes signature) external view returns (bool has, string err)
```

_権限チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| providerStorage | contract IProviderStorage | ProviderStorageコントラクト参照 |
| providerId | bytes32 | プロバイダID |
| hash | bytes32 | signatureの計算元ハッシュ値 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| has | bool | true:権限あり,false:権限なし |
| err | string | エラーメッセージ |

### getProviderData

```solidity
function getProviderData(contract IProviderStorage providerStorage, bytes32 providerId) external view returns (struct ProviderData providerData)
```

_プロバイダデータを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerStorage | contract IProviderStorage | ProviderStorageコントラクト参照 |
| providerId | bytes32 | プロバイダID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerData | struct ProviderData | プロバイダデータ |

### getProviderId

```solidity
function getProviderId(contract IProviderStorage providerStorage) external view returns (bytes32)
```

_プロバイダIDを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerStorage | contract IProviderStorage | ProviderStorageコントラクト参照 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes32 | providerId プロバイダID |

### getZoneName

```solidity
function getZoneName(contract IProviderStorage providerStorage, uint16 zoneId) external view returns (string zoneName)
```

_ゾーン名称を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerStorage | contract IProviderStorage | ProviderStorageコントラクト参照 |
| zoneId | uint16 | ゾーンID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneName | string | ゾーン名称 |

### getZone

```solidity
function getZone(contract IProviderStorage providerStorage) external view returns (uint16 zoneId, string zoneName, string err)
```

_ZoneIDを取得する。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | 領域ID (デジタル通貨区分) |
| zoneName | string | 領域名 (デジタル通貨区分) |
| err | string | bytes(err).length!=0の場合、エラー有り(その他の戻り値は無効) |

### getProviderAll

```solidity
function getProviderAll(contract IProviderStorage providerStorage, bytes32 providerId) external view returns (struct ProviderAll provider)
```

_バックアップ用にプロバイダデータを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerStorage | contract IProviderStorage | ProviderStorageコントラクト参照 |
| providerId | bytes32 | プロバイダID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| provider | struct ProviderAll | 全プロバイダデータ |

### checkAvailableIssuerIds

```solidity
function checkAvailableIssuerIds(contract IProviderStorage providerStorage, contract IContractManager contractManager, uint16 zoneId, bytes32 accountId) external view returns (bool success, string err)
```

_認可イシュアを確認。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerStorage | contract IProviderStorage |  |
| contractManager | contract IContractManager |  |
| zoneId | uint16 | zoneId |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | 成功したかどうか |
| err | string | エラーメッセージ |

