# Solidity API

## FinancialZoneAccountLib

### getAccountLimitData

```solidity
function getAccountLimitData(mapping(bytes32 => struct FinancialZoneAccountData) accountLimitMapping, bytes32 key) external view returns (struct FinancialZoneAccountData accountLimitData)
```

_アカウントの限度額を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitMapping | mapping(bytes32 &#x3D;&gt; struct FinancialZoneAccountData) | 取得対象となるアカウント限度額設定のマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitData | struct FinancialZoneAccountData |  |

### addAccountLimitData

```solidity
function addAccountLimitData(mapping(bytes32 => struct FinancialZoneAccountData) accountLimitDataMapping, bytes32 key, struct AccountLimitValues limitValues) external
```

_アカウント限度額登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitDataMapping | mapping(bytes32 &#x3D;&gt; struct FinancialZoneAccountData) | 登録先のアカウント限度額のマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| limitValues | struct AccountLimitValues | アカウントの限度額値 |

### modAccountLimitData

```solidity
function modAccountLimitData(mapping(bytes32 => struct FinancialZoneAccountData) accountLimitDataMapping, bytes32 key, struct AccountLimitUpdates limitUpdates, struct AccountLimitValues limitValues) external returns (struct AccountLimitValues)
```

_アカウント限度額登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitDataMapping | mapping(bytes32 &#x3D;&gt; struct FinancialZoneAccountData) | 登録先のアカウント限度額のマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| limitUpdates | struct AccountLimitUpdates | アカウント限度額の更新フラグ |
| limitValues | struct AccountLimitValues | アカウントの限度額値 |

### resetCumulative

```solidity
function resetCumulative(mapping(bytes32 => struct FinancialZoneAccountData) accountLimitDataMapping, bytes32 key, uint256 jstDay) external
```

_アカウント限度額初期化_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitDataMapping | mapping(bytes32 &#x3D;&gt; struct FinancialZoneAccountData) | 登録先のアカウント限度額のマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| jstDay | uint256 | 現在の日付 |

### addAmount

```solidity
function addAmount(mapping(bytes32 => struct FinancialZoneAccountData) accountLimitDataMapping, bytes32 key, uint256 amount, uint256 currentDay) external returns (uint256 cumulativeDate, uint256 cumulativeAmount)
```

_累積限度額の加算を行う TODO:減算処理と統合する？_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitDataMapping | mapping(bytes32 &#x3D;&gt; struct FinancialZoneAccountData) | 登録先のアカウント限度額マッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| amount | uint256 | 金額 |
| currentDay | uint256 | 現在の日付 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| cumulativeDate | uint256 |  |
| cumulativeAmount | uint256 |  |

### subtractAmount

```solidity
function subtractAmount(mapping(bytes32 => struct FinancialZoneAccountData) accountLimitDataMapping, bytes32 key, uint256 amount) external returns (uint256 cumulativeDate, uint256 cumulativeAmount)
```

_累積限度額の減算を行う_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitDataMapping | mapping(bytes32 &#x3D;&gt; struct FinancialZoneAccountData) | 登録先のアカウント限度額マッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| amount | uint256 | 金額 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| cumulativeDate | uint256 |  |
| cumulativeAmount | uint256 |  |

### syncLimitAmount

```solidity
function syncLimitAmount(mapping(bytes32 => struct FinancialZoneAccountData) accountLimitDataMapping, bytes32 key, uint256 amount, uint256 currentDay, bytes32 operationType) external
```

_操作額を各種累積限度額に反映させる_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitDataMapping | mapping(bytes32 &#x3D;&gt; struct FinancialZoneAccountData) | 登録先のアカウント限度額マッピングデータ |
| key | bytes32 | アカウントID |
| amount | uint256 | 金額 |
| currentDay | uint256 | 現在の日付 |
| operationType | bytes32 | 取引種別（Constant._SYNC_MINT, _SYNC_BURN, _SYNC_CHARGE, _SYNC_DISCHARGE, _SYNC_TRANSFER） |

