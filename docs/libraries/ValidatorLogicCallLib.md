# Solidity API

## ValidatorLogicCallLib

_ValidatorLogicCallLibライブラリ
     Validatorのview/pure関数を実装するヘルパーライブラリ
     ブロックチェーンデータの変更は行わない_

### checkAddValidatorIsValid

```solidity
function checkAddValidatorIsValid(contract IContractManager contractManager, contract IValidatorStorage validatorStorage, bytes32 issuerId, bytes32 validatorId) external view
```

_検証者追加時の入力パラメータ検証_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | コントラクト管理 |
| validatorStorage | contract IValidatorStorage | 検証者ストレージ |
| issuerId | bytes32 | 申請者ID |
| validatorId | bytes32 | 検証者ID |

### checkValidatorAndAccountExist

```solidity
function checkValidatorAndAccountExist(contract IValidatorStorage validatorStorage, bytes32 validatorId, bytes32 accountId) external view
```

_ValidatorIDとAccountIDの存在チェックを統合_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorageコントラクト参照 |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |

### checkValidatorIdIsValid

```solidity
function checkValidatorIdIsValid(bytes32 validatorId) external pure returns (bool success, string err)
```

_validatorIdの入力検証_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | チェック対象となる検証者ID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:有効,false:無効 |
| err | string | エラーメッセージ |

### checkValidatorExists

```solidity
function checkValidatorExists(bool exists, bytes32 validatorId) external pure returns (bool success, string err)
```

_検証者IDが登録済であるか確認する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| exists | bool | 存在フラグ |
| validatorId | bytes32 | チェック対象となる検証者ID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:登録済,false:未登録 |
| err | string | エラーメッセージ |

### checkAccountExists

```solidity
function checkAccountExists(bool exists, bytes32 accountId) external pure returns (bool success, string err)
```

_指定されたValidatorIDにAccountが紐付いているか確認を行う。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| exists | bool | アカウントIDが検証者IDに紐付き済フラグ |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:紐づいている,false:紐づいていない |
| err | string | エラーメッセージ |

### checkSyncAccountStatusIsValid

```solidity
function checkSyncAccountStatusIsValid(contract IValidatorStorage validatorStorage, bytes32 validatorId, bytes32 accountId, bytes32 accountStatus) external view
```

_アカウント同期時のステータス検証_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |
| accountStatus | bytes32 | アカウントステータス |

### checkValidatorAccountIdExists

```solidity
function checkValidatorAccountIdExists(bytes32 validatorAccountId) external pure returns (bool success, string err)
```

_バリデータが直接管理するアカウントIDの存在確認_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorAccountId | bytes32 | バリデータアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:存在する,false:存在しない |
| err | string | エラーメッセージ |

### getValidatorIdExistence

```solidity
function getValidatorIdExistence(contract IValidatorStorage validatorStorage, bytes32 validatorId) external view returns (bool)
```

_バリデータIDの存在確認をストレージから取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| validatorId | bytes32 | バリデータID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bool | exists 存在フラグ |

### getAccountIdExistenceByValidatorId

```solidity
function getAccountIdExistenceByValidatorId(contract IValidatorStorage validatorStorage, bytes32 validatorId, bytes32 accountId) external view returns (bool)
```

_バリデータIDに紐づくアカウントIDの存在確認をストレージから取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| validatorId | bytes32 | バリデータID |
| accountId | bytes32 | アカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bool | exists 存在フラグ |

### getValidatorData

```solidity
function getValidatorData(contract IValidatorStorage validatorStorage, bytes32 validatorId) external view returns (struct ValidatorData)
```

_バリデータデータをストレージから取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| validatorId | bytes32 | バリデータID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct ValidatorData | validatorData バリデータデータ |

### getValidatorIdsCount

```solidity
function getValidatorIdsCount(contract IValidatorStorage validatorStorage) external view returns (uint256)
```

_バリデータ数をストレージから取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | count バリデータ数 |

### getValidatorIdByIndex

```solidity
function getValidatorIdByIndex(contract IValidatorStorage validatorStorage, uint256 index) external view returns (bytes32)
```

_インデックスによるバリデータIDをストレージから取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| index | uint256 | インデックス |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes32 | validatorId バリデータID |

### getValidatorAll

```solidity
function getValidatorAll(contract IValidatorStorage validatorStorage, uint256 index) external view returns (struct ValidatorAll)
```

_バックアップ用の全バリデータデータを取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| index | uint256 | インデックス |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct ValidatorAll | validator 全バリデータデータ |

### getValidatorList

```solidity
function getValidatorList(contract IValidatorStorage validatorStorage, uint256 limit, uint256 offset) external view returns (struct ValidatorListData[] validators, uint256 totalCount, string err)
```

_検証者情報リストを取得する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorageコントラクト参照 |
| limit | uint256 | limit |
| offset | uint256 | offset |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| validators | struct ValidatorListData[] | validators |
| totalCount | uint256 | validatorの数 |
| err | string | エラーメッセージ |

### checkZoneHasNoError

```solidity
function checkZoneHasNoError(string zoneErr) external pure
```

_Zoneエラーがないことを確認_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneErr | string | Zoneエラー文字列 |

### getZonesForFinancialAccount

```solidity
function getZonesForFinancialAccount(contract IContractManager contractManager, bytes32 accountId, uint16 zoneId) external view returns (struct ZoneData[] zones, string err)
```

_共通領域のアカウントに連携済みのzone情報を取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManager参照 |
| accountId | bytes32 | accountId |
| zoneId | uint16 | ゾーンID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| zones | struct ZoneData[] | zoneIdの配列 |
| err | string | エラーメッセージ |

### checkAddValidatorRoleIsValid

```solidity
function checkAddValidatorRoleIsValid(contract IValidatorStorage validatorStorage, bytes32 validatorId, address validatorEoa) external view
```

_Validator権限追加の検証処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| validatorId | bytes32 | validatorId |
| validatorEoa | address | validatorEoaアドレス |

### checkModValidatorIsValid

```solidity
function checkModValidatorIsValid(contract IValidatorStorage validatorStorage, bytes32 validatorId) external view
```

_Validator名更新の検証処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| validatorId | bytes32 | validatorId |

### checkModAccountIsValid

```solidity
function checkModAccountIsValid(contract IValidatorStorage validatorStorage, bytes32 validatorId, bytes32 accountId) external view
```

_Account名更新の検証処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |

### checkAddAccountIsValid

```solidity
function checkAddAccountIsValid(contract IValidatorStorage validatorStorage, bytes32 validatorId, bytes32 accountId) external view
```

_Account追加の検証処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |

### checkAddValidatorAccountIdIsValid

```solidity
function checkAddValidatorAccountIdIsValid(contract IContractManager contractManager, contract IValidatorStorage validatorStorage, bytes32 validatorId, bytes32 accountId) external view
```

_バリデータが直接管理するアカウントID追加の検証処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManager参照 |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| validatorId | bytes32 | バリデータID |
| accountId | bytes32 | アカウントID |

### getAccountWithLimits

```solidity
function getAccountWithLimits(contract IValidatorStorage validatorStorage, contract IContractManager contractManager, bytes32 validatorId, bytes32 accountId) external view returns (struct AccountDataWithLimitData accountData, string err)
```

_Validatorに紐づくAccountの情報を取得する。（限度額情報付き）_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| contractManager | contract IContractManager | ContractManager参照 |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountData | struct AccountDataWithLimitData | アカウントデータ（限度額情報付き） |
| err | string | エラーメッセージ |

### getValidatorAccountList

```solidity
function getValidatorAccountList(contract IValidatorStorage validatorStorage, contract IContractManager contractManager, bytes32 validatorId, uint256 offset, uint256 limit, string sortOrder) external view returns (struct ValidatorAccountsData[] accounts, uint256 totalCount, string err)
```

_該当ValidatorIDに紐づくAccountの情報を取得する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| contractManager | contract IContractManager | ContractManager参照 |
| validatorId | bytes32 | validatorId |
| offset | uint256 | オフセット |
| limit | uint256 | リミット |
| sortOrder | string | ソート順(desc: 降順, asc: 昇順) |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accounts | struct ValidatorAccountsData[] | ValidatorAccountsData[] |
| totalCount | uint256 |  |
| err | string | エラーメッセージ |

### getValidatorAccountAllList

```solidity
function getValidatorAccountAllList(contract IValidatorStorage validatorStorage, contract IContractManager contractManager, bytes32 validatorId, uint256 offset, uint256 limit) external view returns (struct ValidatorAccountsDataALL[] accounts, uint256 totalCount, string err)
```

_(CoreBatch専用 BCClient経由)該当ValidatorIDに紐づくAccountの情報を取得する。
レスポンスのValidatorAccountsDataのソート順について
validatorMapping[validatorId].accountIdsの格納順の逆順となる。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorStorage | contract IValidatorStorage | ValidatorStorage参照 |
| contractManager | contract IContractManager | ContractManager参照 |
| validatorId | bytes32 | validatorId |
| offset | uint256 | オフセット |
| limit | uint256 | リミット |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accounts | struct ValidatorAccountsDataALL[] | ValidatorAccountsDataALL[] |
| totalCount | uint256 |  |
| err | string | エラーメッセージ |

