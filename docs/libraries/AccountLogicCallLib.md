# Solidity API

## AccountLogicCallLib

_AccountLogicCallLibライブラリ
     Accountのview/pure関数を実装するヘルパーライブラリ
     ブロックチェーンデータの変更は行わない_

### checkToken

```solidity
function checkToken(contract IContractManager contractManager) external view
```

_送金許可設定。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |

### checkTokenAndIBCToken

```solidity
function checkTokenAndIBCToken(contract IContractManager contractManager) external view
```

_送金許可設定。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |

### checkIssuer

```solidity
function checkIssuer(contract IContractManager contractManager) external view
```

_Issuerコントラクトからの呼び出しである事が条件。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |

### checkAddAccountIsValid

```solidity
function checkAddAccountIsValid(contract IContractManager contractManager, contract IAccountStorage accountStorage, bytes32 accountId) external view
```

_addAccountの検証処理（統合版）_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| accountStorage | contract IAccountStorage | AccountStorageコントラクト参照 |
| accountId | bytes32 | アカウントID |

### checkValidator

```solidity
function checkValidator(contract IContractManager contractManager) internal view
```

_の検証処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |

### checkValidatorAndFinance

```solidity
function checkValidatorAndFinance(contract IContractManager contractManager) internal view
```

_の検証処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |

### checkAddAccountRoleIsValid

```solidity
function checkAddAccountRoleIsValid(contract IContractManager contractManager, address accountEoa) external view
```

_AccountのRoleを追加する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| accountEoa | address | accountEoa |

### checkHasAccount

```solidity
function checkHasAccount(contract IAccountStorage accountStorage, bytes32 accountId) public view
```

_AccountID存在確認 本体(内部関数)。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorageコントラクト参照 |
| accountId | bytes32 | accountId |

### hasAccount

```solidity
function hasAccount(contract IAccountStorage accountStorage, bytes32 accountId) internal view returns (bool success, string err)
```

### checkAccountStatusIsValid

```solidity
function checkAccountStatusIsValid(contract IAccountStorage accountStorage, contract IContractManager contractManager, address msgSender, bytes32 accountId, bytes32 accountStatus) external view
```

_Accountの有効性を更新する(凍結 or アクティブ)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorageコントラクト参照 |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| msgSender | address | msgSender |
| accountId | bytes32 | マッピングのキーとなるアカウントID |
| accountStatus | bytes32 | アカウントステータス |

### checkTerminatedIsValid

```solidity
function checkTerminatedIsValid(contract IContractManager contractManager, bytes32 accountId) external view
```

_アカウントのステータスを解約済みに更新する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerコントラクト参照 |
| accountId | bytes32 | マッピングのキーとなるアカウントID |

### getAllBalance

```solidity
function getAllBalance(contract IAccountStorage accountStorage, contract IContractManager contractManager, bytes32 accountId) public view returns (struct AllBalanceData[] allBalance, uint256 totalBalance)
```

FinZoneの残高を最初に、その後BizZoneの残高をZoneID昇順で返す
emitAfterBalanceから呼ばれる関数（権限チェックなし）

_アカウントの全ゾーン残高を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorage 契約リファレンス |
| contractManager | contract IContractManager | ContractManager リファレンス |
| accountId | bytes32 | 残高を取得するアカウントのID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| allBalance | struct AllBalanceData[] | FinZoneとBizZoneの残高配列         - 配列の最初の要素: FinZoneの残高（現在のゾーンの残高）         - 配列の2番目以降: BizZoneの残高（ZoneID昇順でソート済み） |
| totalBalance | uint256 |  |

### getValidatorIdByAccountId

```solidity
function getValidatorIdByAccountId(contract IAccountStorage accountStorage, bytes32 accountId) external view returns (bytes32 validatorId, string err)
```

_アカウントに紐づくバリデータIDを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorage 契約リファレンス |
| accountId | bytes32 | アカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | バリデータID |
| err | string | エラー |

### getAccountBalance

```solidity
function getAccountBalance(contract IAccountStorage accountStorage, bytes32 accountId) external view returns (uint256 balance)
```

_発行後の残高。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage |  |
| accountId | bytes32 | 発行者ID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| balance | uint256 | 発行後の残高 |

### isActivated

```solidity
function isActivated(contract IAccountStorage accountStorage, bytes32 accountId) external view returns (bool success, string err)
```

_アカウントがアクティブかどうかを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorage 契約リファレンス |
| accountId | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool |  |
| err | string |  |

### isTerminated

```solidity
function isTerminated(contract IAccountStorage accountStorage, bytes32 accountId) external view returns (bool terminated)
```

_アカウントが解約状態かどうかを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorage 契約リファレンス |
| accountId | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| terminated | bool |  |

### getAccountDataWithoutZoneId

```solidity
function getAccountDataWithoutZoneId(contract IAccountStorage accountStorage, bytes32 accountId, bool success, string errTmp) external view returns (struct AccountDataWithoutZoneId accountDataWithoutZoneId, string err)
```

_アカウント情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorage 契約リファレンス |
| accountId | bytes32 | マッピングのキーとなるアカウントID |
| success | bool |  |
| errTmp | string |  |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataWithoutZoneId | struct AccountDataWithoutZoneId | アカウントデータ(zoneIdなし) |
| err | string | エラーメッセージ |

### getAccountDataAll

```solidity
function getAccountDataAll(contract IAccountStorage accountStorage, contract IContractManager contractManager, bytes32 accountId) external view returns (struct AccountDataAll accountDataAll)
```

_Accountの全情報を返す。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorage 契約リファレンス |
| contractManager | contract IContractManager |  |
| accountId | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataAll | struct AccountDataAll | アカウントデータ |

### getAccountId

```solidity
function getAccountId(contract IAccountStorage accountStorage, uint256 index) external view returns (bytes32 accountId, string err)
```

_IndexよりAccountIDを取得する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorage 契約リファレンス |
| index | uint256 | index |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |
| err | string | エラーメッセージ |

### getAllowance

```solidity
function getAllowance(contract IAccountStorage accountStorage, bytes32 accountId, bytes32 index) external view returns (uint256 allowance, uint256 approvedAt)
```

_アカウントの許可額を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorage 契約リファレンス |
| accountId | bytes32 | マッピングのキーとなるアカウントID |
| index | bytes32 | 許可対象のアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| allowance | uint256 |  |
| approvedAt | uint256 |  |

### getAllowanceList

```solidity
function getAllowanceList(contract IAccountStorage accountStorage, bytes32 ownerId, uint256 offset, uint256 limit) external view returns (struct AccountApprovalAll[] approvalData, uint256 totalCount, string err)
```

_送金許可一覧照会 TODO:Core APIとのマッピング時に作成_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorage 契約リファレンス |
| ownerId | bytes32 | 送金許可元ID |
| offset | uint256 | オフセット |
| limit | uint256 | リミット |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| approvalData | struct AccountApprovalAll[] | 送金許可設定一覧 |
| totalCount | uint256 | 総数 |
| err | string | エラーメッセージ |

### getAccountCount

```solidity
function getAccountCount(contract IAccountStorage accountStorage) external view returns (uint256 count)
```

_Accountの数を返却する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorage 契約リファレンス |

### getAccountZoneIdList

```solidity
function getAccountZoneIdList(contract IAccountStorage accountStorage, bytes32 accountId) external view returns (uint16[] zoneIdList)
```

_アカウントに連携済みのzoneIdの取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorage 契約リファレンス |
| accountId | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneIdList | uint16[] | アカウントに連携済みのzoneIdのリスト |

### isFrozen

```solidity
function isFrozen(contract IAccountStorage accountStorage, bytes32 accountId) external view returns (bool frozen)
```

_アカウントが凍結状態かどうかを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorage 契約リファレンス |
| accountId | bytes32 | マッピングのキーとなるアカウントID |

### getAccountAll

```solidity
function getAccountAll(contract IAccountStorage accountStorage, uint256 index) external view returns (struct AccountsAll account)
```

_limitとoffsetで指定したAccountsを一括取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountStorage | contract IAccountStorage | AccountStorage 契約リファレンス |
| index | uint256 |  |

