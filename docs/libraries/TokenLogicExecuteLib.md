# Solidity API

## TokenLogicExecuteLib

_TokenLogicExecuteLibライブラリ
     Tokenの実行関数を実装するヘルパーライブラリ_

### executeAddToken

```solidity
function executeAddToken(contract ITokenStorage tokenStorage, bytes32 tokenId, bytes32 name, bytes32 symbol) external
```

_Tokenの追加処理を実行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract ITokenStorage | TokenStorage参照 |
| tokenId | bytes32 | tokenId |
| name | bytes32 | Tokenの名前 |
| symbol | bytes32 | symbol |

### executeModToken

```solidity
function executeModToken(contract ITokenStorage tokenStorage, bytes32 tokenId, bytes32 name, bytes32 symbol) external
```

_Tokenの修正処理を実行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract ITokenStorage | TokenStorage参照 |
| tokenId | bytes32 | tokenId |
| name | bytes32 | Tokenの名前 |
| symbol | bytes32 | symbol |

### executeSetTokenEnabled

```solidity
function executeSetTokenEnabled(contract ITokenStorage tokenStorage, bytes32 tokenId, bool enabled) external
```

_Tokenの有効性設定処理を実行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract ITokenStorage | TokenStorage参照 |
| tokenId | bytes32 | tokenId |
| enabled | bool | 有効性フラグ |

### executeMint

```solidity
function executeMint(contract IContractManager contractManager, contract ITokenStorage tokenStorage, bytes32 accountId, uint256 amount, bytes32 traceId) external returns (uint16 zoneId, bytes32 validatorId, string accountName, uint256 balance)
```

_発行処理を実行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManager参照 |
| tokenStorage | contract ITokenStorage | TokenStorage参照 |
| accountId | bytes32 | accountId |
| amount | uint256 | Mintする数量 |
| traceId | bytes32 | トレースID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | ゾーンID |
| validatorId | bytes32 | バリデーターID |
| accountName | string | アカウント名 |
| balance | uint256 | 残高 |

### executeBurn

```solidity
function executeBurn(contract IContractManager contractManager, contract ITokenStorage tokenStorage, bytes32 accountId, uint256 amount, bytes32 traceId) external returns (uint16 zoneId, bytes32 validatorId, string accountName, uint256 balance)
```

_償却処理を実行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManager参照 |
| tokenStorage | contract ITokenStorage | TokenStorage参照 |
| accountId | bytes32 | accountId |
| amount | uint256 | Burnする数量 |
| traceId | bytes32 | トレースID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | ゾーンID |
| validatorId | bytes32 | バリデーターID |
| accountName | string | アカウント名 |
| balance | uint256 | 残高 |

### executeBurnCancel

```solidity
function executeBurnCancel(contract IContractManager contractManager, contract ITokenStorage tokenStorage, struct BurnCancelData burnCancelData) external returns (uint16 zoneId, bytes32 validatorId, uint256 balance)
```

_償却取り消し処理を実行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManager参照 |
| tokenStorage | contract ITokenStorage | TokenStorage参照 |
| burnCancelData | struct BurnCancelData | 償却取り消しデータ |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | ゾーンID |
| validatorId | bytes32 | バリデーターID |
| balance | uint256 | 残高 |

### executeAddTotalSupply

```solidity
function executeAddTotalSupply(contract ITokenStorage tokenStorage, uint256 amount) external
```

_TotalSupply増額の実行_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract ITokenStorage | TokenStorage参照 |
| amount | uint256 | 増額する数量 |

### executeSubTotalSupply

```solidity
function executeSubTotalSupply(contract ITokenStorage tokenStorage, uint256 amount) external
```

_TotalSupply減額の実行_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract ITokenStorage | TokenStorage参照 |
| amount | uint256 | 減額する数量 |

### executeSetTokenAll

```solidity
function executeSetTokenAll(contract ITokenStorage tokenStorage, struct TokenAll token, uint256 deadline, bytes signature) external
```

_Token全情報設定の実行_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract ITokenStorage | TokenStorage参照 |
| token | struct TokenAll | 設定するToken情報 |
| deadline | uint256 | 署名の期限 |
| signature | bytes | 署名 |

### executeTransferSingle

```solidity
function executeTransferSingle(contract IContractManager contractManager, bytes32 sendAccountId, bytes32 fromAccountId, bytes32 toAccountId, uint256 amount, bytes32 miscValue1, string miscValue2, string memo, bytes32 traceId) external returns (struct TransferData emitData)
```

_単一送金処理を実行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManager参照 |
| sendAccountId | bytes32 | 送信アカウントID |
| fromAccountId | bytes32 | 送信元アカウントID |
| toAccountId | bytes32 | 送信先アカウントID |
| amount | uint256 | 送金額 |
| miscValue1 | bytes32 | その他値1 |
| miscValue2 | string | その他値2 |
| memo | string | メモ |
| traceId | bytes32 | トレースID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| emitData | struct TransferData | 送金データ |

### transfer

```solidity
function transfer(contract IContractManager contractManager, struct TransferData data) public returns (struct TransferData)
```

_送金処理を実行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManager参照 |
| data | struct TransferData | 送金データ |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct TransferData | 送金データ |

