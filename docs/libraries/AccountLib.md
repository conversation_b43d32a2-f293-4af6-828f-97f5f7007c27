# Solidity API

## AccountLib

### getAccountData

```solidity
function getAccountData(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key, bool success, string errTmp) external view returns (struct AccountDataWithoutZoneId accountDataWithoutZoneId, string err)
```

_アカウント情報を取得する TODO: CoreAPIとのマッピング対応時に詳細作成_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 取得対象のアカウントデータが格納されているマッピング |
| key | bytes32 | マッピングのキーとなるアカウントID |
| success | bool |  |
| errTmp | string |  |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataWithoutZoneId | struct AccountDataWithoutZoneId | アカウントデータ(zoneIdなし) |
| err | string | エラーメッセージ |

### getAccountDataAll

```solidity
function getAccountDataAll(address contractManagerAddr, mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 accountId) external view returns (struct AccountDataAll accountDataAll)
```

_アカウントの全情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManagerAddr | address | コントラクトマネージャーのアドレス |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) |  |
| accountId | bytes32 | アカウントID |

### getAllowance

```solidity
function getAllowance(mapping(bytes32 => struct AllowanceList) accountApprovalMapping, bytes32 key, bytes32 index) external view returns (uint256 allowance, uint256 approvedAt)
```

_アカウントの許可額を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountApprovalMapping | mapping(bytes32 &#x3D;&gt; struct AllowanceList) | 取得対象となるアカウントの許可額設定のマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| index | bytes32 | 許可対象のアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| allowance | uint256 |  |
| approvedAt | uint256 |  |

### getAllowanceList

```solidity
function getAllowanceList(mapping(bytes32 => struct AllowanceList) accountApprovalDataMapping, address contractManagerAddr, bytes32 ownerAccountId, uint256 offset, uint256 limit) external view returns (struct AccountApprovalAll[], uint256, string)
```

_アカウントの許可額リストを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountApprovalDataMapping | mapping(bytes32 &#x3D;&gt; struct AllowanceList) | 取得対象となるアカウントの許可額設定のマッピングデータ |
| contractManagerAddr | address | コントラクトマネージャアドレス |
| ownerAccountId | bytes32 | マッピングのキーとなるアカウントID |
| offset | uint256 | offset |
| limit | uint256 | limit |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct AccountApprovalAll[] | allowanceList |
| [1] | uint256 | totalCount |
| [2] | string |  |

### getAccountBalance

```solidity
function getAccountBalance(mapping(bytes32 => struct AccountData) accountData, bytes32 key) external view returns (uint256 balance)
```

_アカウントの残高の取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountData | mapping(bytes32 &#x3D;&gt; struct AccountData) | 取得対象となるアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| balance | uint256 |  |

### getAllBalanceWithAuth

```solidity
function getAllBalanceWithAuth(mapping(bytes32 => struct AccountData) accountData, address contractManagerAddr, bytes32 accountId) external view returns (struct AllBalanceData[] afterBalance, uint256 totalBalance)
```

forceBurnとpartialForceBurnから呼ばれる専用関数
Issuerコントラクトからの呼び出しのみ許可
コントラクトサイズ超過のため、権限チェックロジックを分離
        - 権限チェック処理をgetAllBalanceから分離してこちらに集約
        - 実際の残高取得処理は共通のgetAllBalance関数に委譲

_アカウントの全ゾーン残高を取得する（権限チェック付き）_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountData | mapping(bytes32 &#x3D;&gt; struct AccountData) | アカウントデータのマッピング |
| contractManagerAddr | address | コントラクトマネージャーのアドレス |
| accountId | bytes32 | 残高を取得するアカウントのID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| afterBalance | struct AllBalanceData[] | FinZoneとBizZoneの残高配列 |
| totalBalance | uint256 | 全ゾーンの合計残高 |

### getAllBalance

```solidity
function getAllBalance(mapping(bytes32 => struct AccountData) accountData, address contractManagerAddr, bytes32 accountId) public view returns (struct AllBalanceData[] allBalance, uint256 totalBalance)
```

FinZoneの残高を最初に、その後BizZoneの残高をZoneID昇順で返す
emitAfterBalanceから呼ばれる関数（権限チェックなし）

_アカウントの全ゾーン残高を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountData | mapping(bytes32 &#x3D;&gt; struct AccountData) | アカウントデータのマッピング |
| contractManagerAddr | address | コントラクトマネージャーのアドレス |
| accountId | bytes32 | 残高を取得するアカウントのID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| allBalance | struct AllBalanceData[] | FinZoneとBizZoneの残高配列         - 配列の最初の要素: FinZoneの残高（現在のゾーンの残高）         - 配列の2番目以降: BizZoneの残高（ZoneID昇順でソート済み） |
| totalBalance | uint256 |  |

### getAccountZoneIdList

```solidity
function getAccountZoneIdList(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key) external view returns (uint16[] zoneIdList)
```

_アカウントに連携済みのzoneIdの取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 取得対象となるアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneIdList | uint16[] | アカウントに連携済みのzoneIdのリスト |

### getValidatorIdByAccountId

```solidity
function getValidatorIdByAccountId(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key) external view returns (bytes32 validatorId)
```

アカウントに連携されているvalidatorIdを取得

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 取得対象となるアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 |  |

### isActivated

```solidity
function isActivated(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key) external view returns (bool success, string err)
```

_アカウントがアクティブかどうかを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 取得対象となるアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool |  |
| err | string |  |

### isFrozen

```solidity
function isFrozen(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key) external view returns (bool frozen)
```

_アカウントが凍結状態かどうかを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 取得対象となるアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |

### forceBurn

```solidity
function forceBurn(mapping(bytes32 => struct AccountData) accountDataMapping, address contractManagerAddr, bytes32 accountId) external returns (uint256 burnedAmount, uint256 burnedBalance, struct ForceDischarge[] forceDischarge)
```

_アカウントを強制償却させる_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 償却対象となるアカウントのマッピングデータ |
| contractManagerAddr | address |  |
| accountId | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| burnedAmount | uint256 | 償却額 |
| burnedBalance | uint256 |  |
| forceDischarge | struct ForceDischarge[] |  |

### partialForceBurn

```solidity
function partialForceBurn(mapping(bytes32 => struct AccountData) accountDataMapping, contract IContractManager contractManager, bytes32 accountId, uint256 burnedAmount, uint256 burnedBalance) external returns (struct ForceDischarge[] forceDischarge)
```

_アカウントを部分的に強制償却させる_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 償却対象となるアカウントのマッピングデータ |
| contractManager | contract IContractManager |  |
| accountId | bytes32 | マッピングのキーとなるアカウントID |
| burnedAmount | uint256 | 償却する金額 |
| burnedBalance | uint256 | 償却後に残す金額 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| forceDischarge | struct ForceDischarge[] | ディスチャージしたBizゾーン情報 |

### isTerminated

```solidity
function isTerminated(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key) external view returns (bool terminated)
```

_アカウントが解約状態かどうかを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 取得対象となるアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| terminated | bool |  |

### addAccountData

```solidity
function addAccountData(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key, string accountName) external
```

_アカウントの登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 登録先のアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| accountName | string | アカウント名 |

### addValidatorId

```solidity
function addValidatorId(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key, bytes32 validatorId) external
```

_アカウントに紐づくvalidatorIdの登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 登録先のアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| validatorId | bytes32 | バリデータID |

### addAccountId

```solidity
function addAccountId(bytes32[] accountIdList, bytes32 key) external
```

_アカウントID登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountIdList | bytes32[] | アカウントIDの配列 |
| key | bytes32 | 追加対象のアカウントID |

### modAccount

```solidity
function modAccount(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key, string accountName) external
```

_アカウント名の変更_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 登録先のアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| accountName | string | アカウント名 |

### addAccountIdExistence

```solidity
function addAccountIdExistence(mapping(bytes32 => bool) accountIdExistenceMapping, bytes32 key, bool isExist) external
```

_アカウント存在確認フラグ登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountIdExistenceMapping | mapping(bytes32 &#x3D;&gt; bool) | アカウント存在確認対象のマッピング |
| key | bytes32 | 追加対象のアカウントID |
| isExist | bool | true:存在 / false:削除 |

### setAccountStatus

```solidity
function setAccountStatus(mapping(bytes32 => struct AccountData) accountDataMapping, address contractManagerAddr, address msgSender, bytes32 key, bytes32 accountStatus, bytes32 reasonCode) external
```

_Accountの有効性を更新する(凍結 or アクティブ)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 登録先のアカウントのマッピングデータ |
| contractManagerAddr | address | コントラクトマネージャーアドレス |
| msgSender | address |  |
| key | bytes32 | マッピングのキーとなるアカウントID |
| accountStatus | bytes32 | アカウントステータス |
| reasonCode | bytes32 | 理由コード |

### setApproval

```solidity
function setApproval(mapping(bytes32 => struct AllowanceList) accountApprovalMapping, bytes32 ownerId, bytes32 spenderId, string spenderName, uint256 approvedAt, uint256 amount) external
```

_アカウント許可額設定_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountApprovalMapping | mapping(bytes32 &#x3D;&gt; struct AllowanceList) | 登録先のアカウント許可額データマッピング |
| ownerId | bytes32 | マッピングのキーとなる所有者ID |
| spenderId | bytes32 | 支払い許可対象となるアカウントID |
| spenderName | string | 支払い許可対象となるアカウント名 |
| approvedAt | uint256 | 支払い許可日時 |
| amount | uint256 | 支払い許可額 |

### addZone

```solidity
function addZone(mapping(bytes32 => struct AccountData) accountDataMaping, bytes32 key, uint16 zoneId) external
```

_連携済みゾーン情報の追加_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMaping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 登録先のアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| zoneId | uint16 | ゾーンID |

### setTerminated

```solidity
function setTerminated(mapping(bytes32 => struct AccountData) accountDataMaping, address contractManagerAddr, address msgSender, bytes32 key, bytes32 reasonCode) external
```

_アカウントのステータスを解約済みに更新する　TODO:他関数と統合する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMaping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 登録先のアカウントのマッピングデータ |
| contractManagerAddr | address | コントラクトマネージャーアドレス |
| msgSender | address |  |
| key | bytes32 | マッピングのキーとなるアカウントID |
| reasonCode | bytes32 | 理由コード |

### setBalance

```solidity
function setBalance(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key, uint256 amount, bool isAddition) external returns (uint256 balance)
```

_残高編集_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 登録先のアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| amount | uint256 | 発行額 |
| isAddition | bool | true:加算 / false:減産 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| balance | uint256 | 発行後の残高 |

### setAllowance

```solidity
function setAllowance(mapping(bytes32 => struct AllowanceList) accountApprovalDataMapping, bytes32 key, bytes32 spenderId, uint256 amount) external
```

_送金許可額の減額を行う_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountApprovalDataMapping | mapping(bytes32 &#x3D;&gt; struct AllowanceList) | 登録先のアカウント送金許可額のマッピングデータ |
| key | bytes32 | マッピングのキーとなる所有者ID |
| spenderId | bytes32 | 送金許可対象者のID |
| amount | uint256 | 送金許可額 |

