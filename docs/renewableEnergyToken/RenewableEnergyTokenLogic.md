# Solidity API

## RenewableEnergyTokenLogic

### initialize

```solidity
function initialize(contract IRenewableEnergyTokenStorage renewableEnergyTokenStorage, contract ITransferable token, address contractManager) public
```

_Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergyTokenStorage | contract IRenewableEnergyTokenStorage |  |
| token | contract ITransferable |  |
| contractManager | address | ContractManagerアドレス |

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | コントラクトバージョン |

### mint

```solidity
function mint(bytes32 tokenId, bytes32 metadataId, bytes32 metadataHash, bytes32 mintAccountId, bytes32 ownerAccountId, bool isLocked, bytes32 traceId) external
```

_トークンを発行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | トークンID |
| metadataId | bytes32 | メタデータID |
| metadataHash | bytes32 | メタデータハッシュ |
| mintAccountId | bytes32 | ミントアカウントID |
| ownerAccountId | bytes32 | オーナーアカウントID |
| isLocked | bool | ロック状態 |
| traceId | bytes32 | トレースID |

### transfer

```solidity
function transfer(bytes32 fromAccountId, bytes32 toAccountId, bytes32 tokenId, bytes32 traceId) external
```

_トークンを移転する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| fromAccountId | bytes32 | 移転元アカウントID |
| toAccountId | bytes32 | 移転先アカウントID |
| tokenId | bytes32 | トークンID |
| traceId | bytes32 | トレースID |

### customTransfer

```solidity
function customTransfer(bytes32 sendAccountId, bytes32 fromAccountId, bytes32 toAccountId, uint256 amount, bytes32 miscValue1, string miscValue2, string memo, bytes32 traceId) external returns (bool result)
```

_トークンを移転する(カスタムトランスファー)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| sendAccountId | bytes32 | SendAccount |
| fromAccountId | bytes32 | FromAccount |
| toAccountId | bytes32 | toAccount |
| amount | uint256 | 金額 |
| miscValue1 | bytes32 | カスタムコントラクト用パラメータ1 |
| miscValue2 | string | カスタムコントラクト用パラメータ2 |
| memo | string | メモ |
| traceId | bytes32 | トレースID |

### restoreRenewableEnergyTokens

```solidity
function restoreRenewableEnergyTokens(struct RenewableEnergyTokenAll[] renewableEnergytokens, uint256 deadline, bytes signature) external
```

_RenewableEnergyTokenAll情報を登録、もしくは上書きする
     バックアップリスト作業時のみ実行_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergytokens | struct RenewableEnergyTokenAll[] | renewableEnergytokens |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### restoreTokenIdsByAccountId

```solidity
function restoreTokenIdsByAccountId(struct TokenIdsByAccountIdAll[] tokenIdsByAccountId, uint256 deadline, bytes signature) external
```

_TokenIdsByAccountIdAll情報を登録、もしくは上書きする
     バックアップリスト作業時のみ実行_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenIdsByAccountId | struct TokenIdsByAccountIdAll[] | tokenIdsByAccountId |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### checkTransaction

```solidity
function checkTransaction(bytes32 sendAccountId, bytes32 fromAccountId, bytes32 toAccountId, bytes32 miscValue1, string miscValue2) external view returns (bool success, string err)
```

_トークンの移転チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| sendAccountId | bytes32 | 送信元アカウントID |
| fromAccountId | bytes32 | 移転元アカウントID |
| toAccountId | bytes32 | 移転先アカウントID |
| miscValue1 | bytes32 | miscValue1 |
| miscValue2 | string | miscValue2 |

### getTokenList

```solidity
function getTokenList(bytes32 validatorId, bytes32 accountId, uint256 offset, uint256 limit, string sortOrder) external view returns (struct RenewableEnergyTokenListData[] renewableEnergyTokenList, uint256 totalCount, string err)
```

_アカウントに紐づくトークンの一覧を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 |  |
| accountId | bytes32 | アカウントID |
| offset | uint256 | offset |
| limit | uint256 | limit |
| sortOrder | string | sortOrder(true: 降順, false: 昇順) |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergyTokenList | struct RenewableEnergyTokenListData[] | トークンデータ一覧 |
| totalCount | uint256 |  |
| err | string |  |

### getToken

```solidity
function getToken(bytes32 tokenId) external view returns (struct RenewableEnergyTokenData renewableEnergyTokenData, string err)
```

_トークンの詳細情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | トークンID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergyTokenData | struct RenewableEnergyTokenData | トークンデータ |
| err | string |  |

### hasToken

```solidity
function hasToken(bytes32 tokenId, bytes32 accountId) external view returns (bool success, string err)
```

_引数のアカウントIDが、引数のトークンIDのNFTを所有しているか確認する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | トークンID |
| accountId | bytes32 | アカウントID |

### getTokenCount

```solidity
function getTokenCount() external view returns (uint256 count)
```

_Tokenの数を返却する。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| count | uint256 | tokenの数 |

### backupRenewableEnergyTokens

```solidity
function backupRenewableEnergyTokens(uint256 offset, uint256 limit, uint256 deadline, bytes signature) external view returns (struct RenewableEnergyTokenAll[] renewableEnergyTokenAll, uint256 totalCount, string err)
```

_limitとoffsetで指定したRenewableEnergyTokensを一括取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| offset | uint256 | オフセット |
| limit | uint256 | 取得件数 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergyTokenAll | struct RenewableEnergyTokenAll[] | 全RenewableEnergyTokenの情報 |
| totalCount | uint256 | 取得件数 |
| err | string | エラーメッセージ |

### backupTokenIdsByAccountIds

```solidity
function backupTokenIdsByAccountIds(uint256 offset, uint256 limit, uint256 deadline, bytes signature) external view returns (struct TokenIdsByAccountIdAll[] tokenIdsByAccountIdAll, uint256 totalCount, string err)
```

_limitとoffsetで指定したTokenIdsByAccountIdsを一括取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| offset | uint256 | オフセット |
| limit | uint256 | 取得件数 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenIdsByAccountIdAll | struct TokenIdsByAccountIdAll[] | 全TokenIdsByAccountIdの情報 |
| totalCount | uint256 | 取得件数 |
| err | string | エラーメッセージ |

### getRenewableEnergyTokenAll

```solidity
function getRenewableEnergyTokenAll(uint256 index) external view returns (struct RenewableEnergyTokenAll renewableEnergyToken)
```

_RenewableEnergyToken全情報取得
     既に登録されているRenewableEnergyToken全情報取得を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| index | uint256 | オフセット |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergyToken | struct RenewableEnergyTokenAll | 全Tokenの情報 |

### getTokenIdsByAccountIdAll

```solidity
function getTokenIdsByAccountIdAll(bytes32 accountId) external view returns (struct TokenIdsByAccountIdAll tokenIdsByAccountId)
```

_指定されたaccountIdに紐づくTokenIdを取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |

