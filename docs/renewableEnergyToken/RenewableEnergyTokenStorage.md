# Solidity API

## RenewableEnergyTokenStorage

### onlyRenewableEnergyTokenLogic

```solidity
modifier onlyRenewableEnergyTokenLogic()
```

_TokenLogicコントラクトからのみ呼び出し可能を保証するmodifier_

### adminOnly

```solidity
modifier adminOnly(bytes32 hash, uint256 deadline, bytes signature)
```

### initialize

```solidity
function initialize(contract IContractManager contractManager, address renewableEnergyTokenLogicAddr) public
```

_初期化関数_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerアドレス |
| renewableEnergyTokenLogicAddr | address | TokenLogicコントラクトアドレス |

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | コントラクトバージョン |

### getTokenById

```solidity
function getTokenById(bytes32 tokenId) external view returns (struct RenewableEnergyTokenData)
```

_トークンの詳細情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | トークンID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct RenewableEnergyTokenData | renewableEnergyTokenData トークンデータ |

### setNewAccountIdForToken

```solidity
function setNewAccountIdForToken(bytes32 fromAccountId, bytes32 toAccountId, bytes32 tokenId) external
```

_指定されたtokenIdの所有者と前の所有者を更新する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| fromAccountId | bytes32 | 移転元アカウントID |
| toAccountId | bytes32 | 移転先アカウントID |
| tokenId | bytes32 | トークンID |

### addTokenId

```solidity
function addTokenId(bytes32 tokenId) external
```

_mint関数のtokenId配列にtokenIdを追加する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | トークンID |

### addTokenIdToAccountId

```solidity
function addTokenIdToAccountId(bytes32 accountId, bytes32 tokenId) external
```

_mint関数でaccountIdにtokenIdを追加する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| tokenId | bytes32 | トークンID |

### addToken

```solidity
function addToken(bytes32 tokenId, bytes32 metadataId, bytes32 metadataHash, bytes32 mintAccountId, bytes32 ownerAccountId, bool isLocked) external
```

_トークンを追加する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | トークンID |
| metadataId | bytes32 | メタデータID |
| metadataHash | bytes32 | メタデータハッシュ |
| mintAccountId | bytes32 | 発行するアカウントID |
| ownerAccountId | bytes32 | 所有者のアカウントID |
| isLocked | bool | トークンのロック状態 |

### removeTokenIdFromAccountId

```solidity
function removeTokenIdFromAccountId(bytes32 accountId, bytes32 tokenId) external
```

_移転元アカウントの該当トークン所有情報を削除する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | 移転もとのアカウントID |
| tokenId | bytes32 | キーとなるトークンID |

### setRenewableEnergyTokenAll

```solidity
function setRenewableEnergyTokenAll(struct RenewableEnergyTokenAll token, uint256 deadline, bytes signature) external
```

_指定されたtokenIdに紐づくToken情報を登録、もしくは上書きする_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| token | struct RenewableEnergyTokenAll | Tokenの情報 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### setTokenIdsByAccountIdAll

```solidity
function setTokenIdsByAccountIdAll(struct TokenIdsByAccountIdAll tokenIdByAccountId, uint256 deadline, bytes signature) external
```

_指定されたaccountIdに紐づくTokenIdを登録、もしくは上書きする_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenIdByAccountId | struct TokenIdsByAccountIdAll | accountIdに紐づくTokenId |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### getToken

```solidity
function getToken(bytes32 tokenId) external view returns (struct RenewableEnergyTokenData renewableEnergyTokenData, string err)
```

_トークンの詳細情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | トークンID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergyTokenData | struct RenewableEnergyTokenData | トークンデータ |
| err | string | エラーメッセージ |

### getTokenCount

```solidity
function getTokenCount() external view returns (uint256 count)
```

_Tokenの数を返却する。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| count | uint256 | tokenの数 |

### getRenewableEnergyTokenAll

```solidity
function getRenewableEnergyTokenAll(uint256 index) external view returns (struct RenewableEnergyTokenAll renewableEnergyToken)
```

_指定されたインデックスのRenewableEnergyTokenAll情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| index | uint256 | 取得tokensのindex |

### getTokenIdsByIndex

```solidity
function getTokenIdsByIndex(uint256 index) external view returns (bytes32)
```

_指定されたインデックスに対応するToken IDを返却する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| index | uint256 | 取得したいTokenのインデックス |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes32 | tokenId 対応するTokenのID |

### getTokenIdsByAccountIdAll

```solidity
function getTokenIdsByAccountIdAll(bytes32 accountId) external view returns (struct TokenIdsByAccountIdAll tokenIdsByAccountId)
```

_指定されたaccountIdに紐づくTokenIdを取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenIdsByAccountId | struct TokenIdsByAccountIdAll | accountIdに紐づくTokenIdのリスト |

