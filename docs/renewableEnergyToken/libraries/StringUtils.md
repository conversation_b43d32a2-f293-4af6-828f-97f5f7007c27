# Solidity API

## StringUtils

### stringToBytes32

```solidity
function stringToBytes32(string source) internal pure returns (bytes32 result)
```

_stringをbytes32に変換する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| source | string | 変換対象のstring |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| result | bytes32 | 変換後のbytes32 |

### slice

```solidity
function slice(string source, string delimiter) internal pure returns (string[] result)
```

_stringの文字列を配列に変換する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| source | string | 変換対象のstring |
| delimiter | string | 区切り文字 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| result | string[] | 変換後の配列 |

### substring

```solidity
function substring(string str, uint256 startIndex, uint256 endIndex) internal pure returns (string)
```

_配列内の特定の位置の文字列を取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| str | string | string |
| startIndex | uint256 | startIndex |
| endIndex | uint256 | endIndex |

