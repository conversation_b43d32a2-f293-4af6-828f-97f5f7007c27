# Solidity API

## RenewableEnergyTokenLogicCallLib

_TokenLogicCallLibライブラリ
     Tokenのview/pure関数を実装するヘルパーライブラリ
     ブロックチェーンデータの変更は行わない_

### mintIsValid

```solidity
function mintIsValid(contract IRenewableEnergyTokenStorage tokenStorage, bytes32 tokenId) external view
```

_トークン発行の妥当性を検証する (validate mint)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract IRenewableEnergyTokenStorage |  |
| tokenId | bytes32 | トークンID |

### hasToken

```solidity
function hasToken(contract IRenewableEnergyTokenStorage tokenStorage, bytes32 tokenId, bytes32 accountId) internal view returns (bool success, string err)
```

_引数のアカウントIDが、引数のトークンIDのNFTを所有しているか確認する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract IRenewableEnergyTokenStorage |  |
| tokenId | bytes32 | トークンID |
| accountId | bytes32 | アカウントID |

### transferIsValid

```solidity
function transferIsValid(contract IRenewableEnergyTokenStorage _renewableEnergyTokenStorage, bytes32 fromAccountId, bytes32 toAccountId, bytes32 tokenId) external view
```

_トークン移転の妥当性を検証する (validate transfer)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| _renewableEnergyTokenStorage | contract IRenewableEnergyTokenStorage |  |
| fromAccountId | bytes32 | 移転元アカウントID |
| toAccountId | bytes32 | 移転先アカウントID |
| tokenId | bytes32 | トークンID |

### customTransferIsValid

```solidity
function customTransferIsValid(contract ITransferable _token, bytes32 fromAccountId, bytes32 toAccountId, bytes32 miscValue1, string miscValue2) external pure
```

_カスタムトランスファーの妥当性を検証する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| _token | contract ITransferable |  |
| fromAccountId | bytes32 | FromAccount |
| toAccountId | bytes32 | toAccount |
| miscValue1 | bytes32 | カスタムコントラクト用パラメータ1 |
| miscValue2 | string | カスタムコントラクト用パラメータ2 |

### getTokenList

```solidity
function getTokenList(contract IRenewableEnergyTokenStorage tokenStorage, contract IContractManager contractManager, bytes32 validatorId, bytes32 accountId, uint256 offset, uint256 limit) external view returns (struct RenewableEnergyTokenListData[] tokenDataList, uint256 totalCount, string err)
```

_renewableEnergyTokenDataMappingのマッピング内から、指定されたaccountIdがownerAccountId、mintAccountIdとなっているトークンのリストを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract IRenewableEnergyTokenStorage |  |
| contractManager | contract IContractManager |  |
| validatorId | bytes32 |  |
| accountId | bytes32 | 取得対象のアカウントID |
| offset | uint256 | 取得開始位置 |
| limit | uint256 | 取得件数 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenDataList | struct RenewableEnergyTokenListData[] | 取得したトークンのリスト |
| totalCount | uint256 | 取得対象のトークン数 |
| err | string | エラーメッセージ |

### getTokenCountForGetTokenListFunction

```solidity
function getTokenCountForGetTokenListFunction(contract IRenewableEnergyTokenStorage tokenStorage, bytes32 accountId) internal view returns (uint256 tokenCount)
```

_GetTokenList関数のTokenCountを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract IRenewableEnergyTokenStorage |  |
| accountId | bytes32 | 取得対象のアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenCount | uint256 | 取得対象のトークン数 |

### buildTokenDataListForGetTokenListFunction

```solidity
function buildTokenDataListForGetTokenListFunction(contract IRenewableEnergyTokenStorage tokenStorage, bytes32 accountId, uint256 offset, uint256 limit, uint256 tokenCount) internal view returns (struct RenewableEnergyTokenListData[] tokenDataList)
```

_GetTokenList関数用のTokenDataListを構築する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract IRenewableEnergyTokenStorage |  |
| accountId | bytes32 | 取得対象のアカウントID |
| offset | uint256 | 取得開始位置 |
| limit | uint256 | 取得件数 |
| tokenCount | uint256 | 取得対象のトークン数 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenDataList | struct RenewableEnergyTokenListData[] | 取得したトークンのリスト |

### getToken

```solidity
function getToken(contract IRenewableEnergyTokenStorage tokenStorage, bytes32 tokenId) external view returns (struct RenewableEnergyTokenData renewableEnergyTokenData, string err)
```

_トークンの詳細情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract IRenewableEnergyTokenStorage |  |
| tokenId | bytes32 | トークンID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergyTokenData | struct RenewableEnergyTokenData | トークンデータ |
| err | string | エラーメッセージ |

### checkTransactionIsValid

```solidity
function checkTransactionIsValid(contract IRenewableEnergyTokenStorage tokenStorage, address contractManagerAddr, bytes32 sendAccountId, bytes32 fromAccountId, bytes32 toAccountId, bytes32 miscValue1, string[] miscValue2Arrays) external view returns (bool success, string err)
```

_トークンが移転可能かどうかチェックする_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract IRenewableEnergyTokenStorage |  |
| contractManagerAddr | address | コントラクトマネージャーアドレス |
| sendAccountId | bytes32 | 送信者のアカウントID |
| fromAccountId | bytes32 | 移転元のアカウントID |
| toAccountId | bytes32 | 移転先のアカウントID |
| miscValue1 | bytes32 | miscValue1 |
| miscValue2Arrays | string[] | miscValue2 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:OK,false:NG |
| err | string | エラーメッセージ |

### getRenewableEnergyTokenAll

```solidity
function getRenewableEnergyTokenAll(contract IRenewableEnergyTokenStorage tokenStorage, uint256 index) external view returns (struct RenewableEnergyTokenAll renewableEnergyToken)
```

_RenewableEnergyToken全情報取得
     既に登録されているRenewableEnergyToken全情報取得を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract IRenewableEnergyTokenStorage |  |
| index | uint256 | オフセット |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergyToken | struct RenewableEnergyTokenAll | 全Tokenの情報 |

### getTokenIdsByAccountIdAll

```solidity
function getTokenIdsByAccountIdAll(contract IRenewableEnergyTokenStorage tokenStorage, bytes32 accountId) external view returns (struct TokenIdsByAccountIdAll tokenIdsByAccountId)
```

_指定されたaccountIdに紐づくTokenIdを取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract IRenewableEnergyTokenStorage |  |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenIdsByAccountId | struct TokenIdsByAccountIdAll | accountIdに紐づくTokenIdのリスト |

### getTokenCount

```solidity
function getTokenCount(contract IRenewableEnergyTokenStorage tokenStorage) external view returns (uint256 count)
```

_Tokenの数を返却する。_

### backupRenewableEnergyTokens

```solidity
function backupRenewableEnergyTokens(contract IRenewableEnergyTokenStorage tokenStorage, contract IContractManager contractManager, uint256 offset, uint256 limit, uint256 deadline, bytes signature) external view returns (struct RenewableEnergyTokenAll[] renewableEnergyTokenAll, uint256 totalCount, string err)
```

_limitとoffsetで指定したRenewableEnergyTokensを一括取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract IRenewableEnergyTokenStorage |  |
| contractManager | contract IContractManager |  |
| offset | uint256 | オフセット |
| limit | uint256 | 取得件数 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergyTokenAll | struct RenewableEnergyTokenAll[] | 全RenewableEnergyTokenの情報 |
| totalCount | uint256 | 取得件数 |
| err | string | エラーメッセージ |

### backupTokenIdsByAccountIds

```solidity
function backupTokenIdsByAccountIds(contract IRenewableEnergyTokenStorage tokenStorage, contract IContractManager contractManager, uint256 offset, uint256 limit, uint256 deadline, bytes signature) external view returns (struct TokenIdsByAccountIdAll[] tokenIdsByAccountIdAll, uint256 totalCount, string err)
```

_limitとoffsetで指定したTokenIdsByAccountIdsを一括取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenStorage | contract IRenewableEnergyTokenStorage |  |
| contractManager | contract IContractManager |  |
| offset | uint256 | オフセット |
| limit | uint256 | 取得件数 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenIdsByAccountIdAll | struct TokenIdsByAccountIdAll[] | 全TokenIdsByAccountIdの情報 |
| totalCount | uint256 | 取得件数 |
| err | string | エラーメッセージ |

