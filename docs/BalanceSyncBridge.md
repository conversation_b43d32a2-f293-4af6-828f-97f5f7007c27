# Solidity API

## BalanceSyncBridge

### finZoneId

```solidity
uint16 finZoneId
```

### balanceSyncSourcePort

```solidity
string balanceSyncSourcePort
```

### balanceSyncSourceChannel

```solidity
string balanceSyncSourceChannel
```

### balanceSyncSourceVersion

```solidity
string balanceSyncSourceVersion
```

### adminOnly

```solidity
modifier adminOnly(uint256 deadline, bytes signature)
```

### initialize

```solidity
function initialize(contract IIBCHandler ibcHandler_, address ibcTokenAddr, address accountAddr, address accessCtrlAddr) public
```

_Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| ibcHandler_ | contract IIBCHandler | ibcHandler |
| ibcTokenAddr | address | ibcToken |
| accountAddr | address | account |
| accessCtrlAddr | address | accessCtrl |

### version

```solidity
function version() external pure virtual returns (string)
```

_コントラクトバージョン取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | version コントラクトバージョン |

### ibcAddress

```solidity
function ibcAddress() public view virtual returns (address)
```

_Returns the address of the IBC contract._

### setAddress

```solidity
function setAddress(contract IIBCToken ibcTokenAddr, contract IAccount accountAddr, contract IAccessCtrl accessCtrlAddr, uint256 deadline, bytes signature) public
```

### setChannel

```solidity
function setChannel(uint16 zoneId, string channel, uint256 deadline, bytes signature) public
```

### getConfig

```solidity
function getConfig() external view returns (struct Config config)
```

### syncTransfer

```solidity
function syncTransfer(bytes32 fromAccountId, bytes32 toAccountId, uint16 fromZoneId, uint256 amount, uint64 timeoutHeight, bytes32 traceId) external returns (uint256)
```

_zone内のtransferを実行する。bizzone内で実行された場合はfinzoneへ残高同期のpacketを送信する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| fromAccountId | bytes32 | 送り元のアカウントID |
| toAccountId | bytes32 | 送り先のアカウントID |
| fromZoneId | uint16 | packet送信元のzoneID |
| amount | uint256 | amount |
| timeoutHeight | uint64 | timeout までの Block高 |
| traceId | bytes32 | traceId |

### onRecvPacket

```solidity
function onRecvPacket(struct Packet.Data packet, address) external returns (bytes acknowledgement)
```

_relayerからpacketを受け取り、Financial Zoneで管理するBusiness Zoneのステータスを更新する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| packet | struct Packet.Data | packet |
|  | address |  |

### onChanOpenInit

```solidity
function onChanOpenInit(struct IIBCModule.MsgOnChanOpenInit msg_) external virtual returns (string)
```

### onChanOpenTry

```solidity
function onChanOpenTry(struct IIBCModule.MsgOnChanOpenTry msg_) external virtual returns (string)
```

### recoverPacket

```solidity
function recoverPacket(struct Packet.Data packet, address, uint256 deadline, bytes signature) external
```

_リカバリーのためADMIN権限を利用してpacket receiveを再実行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| packet | struct Packet.Data | packet |
|  | address |  |
| deadline | uint256 |  |
| signature | bytes |  |

