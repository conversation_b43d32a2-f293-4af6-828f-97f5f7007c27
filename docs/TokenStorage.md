# Solidity API

## TokenStorage

_TokenStorageコントラクト
     Tokenデータのストレージ管理を行う
     CRUDのみを実装し、ビジネスロジックは含まない_

### onlyTokenLogic

```solidity
modifier onlyTokenLogic()
```

_TokenLogicコントラクトからのみ呼び出し可能を保証するmodifier_

### adminOnly

```solidity
modifier adminOnly(bytes32 hash, uint256 deadline, bytes signature)
```

### initialize

```solidity
function initialize(contract IContractManager contractManager, address tokenLogicAddr) public
```

_初期化関数_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerアドレス |
| tokenLogicAddr | address | TokenLogicコントラクトアドレス |

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | コントラクトバージョン |

### getTokenData

```solidity
function getTokenData(bytes32 tokenId) external view returns (struct TokenData tokenData)
```

_トークンデータを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | トークンID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenData | struct TokenData | トークンデータ |

### setTokenData

```solidity
function setTokenData(bytes32 tokenId, struct TokenData tokenData) external
```

_トークンデータを設定する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | トークンID |
| tokenData | struct TokenData | トークンデータ |

### updateToken

```solidity
function updateToken(bytes32 tokenId, bytes32 name, bytes32 symbol) external
```

_トークンの名前とシンボルを更新する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | トークンID |
| name | bytes32 | 新しい名前 |
| symbol | bytes32 | 新しいシンボル |

### setTokenEnabled

```solidity
function setTokenEnabled(bytes32 tokenId, bool enabled) external
```

_トークンの有効性を設定する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | トークンID |
| enabled | bool | 有効性フラグ |

### addTotalSupply

```solidity
function addTotalSupply(bytes32 tokenId, uint256 amount) external
```

_トークンの総供給量を増加させる_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | トークンID |
| amount | uint256 | 増加量 |

### subTotalSupply

```solidity
function subTotalSupply(bytes32 tokenId, uint256 amount) external
```

_トークンの総供給量を減少させる_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | トークンID |
| amount | uint256 | 減少量 |

### getTokenId

```solidity
function getTokenId() external view returns (bytes32 tokenId)
```

_現在のトークンIDを取得する_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | 現在のトークンID |

### setTokenId

```solidity
function setTokenId(bytes32 tokenId) external
```

_現在のトークンIDを設定する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | 設定するトークンID |

### setTokenAll

```solidity
function setTokenAll(struct TokenAll token, uint256 deadline, bytes signature) external
```

_全トークンデータを設定する（バックアップ・リストア用）_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| token | struct TokenAll | 全トークンデータ |
| deadline | uint256 |  |
| signature | bytes |  |

### getTokenAll

```solidity
function getTokenAll() external view returns (struct TokenAll token)
```

_全トークンデータを取得する（バックアップ・リストア用）_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| token | struct TokenAll | 全トークンデータ |

