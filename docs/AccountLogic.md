# Solidity API

## AccountLogic

_AccountLogicコントラクト_

### initialize

```solidity
function initialize(contract IContractManager contractManager, contract IAccountStorage accountStorage) public
```

_初期化関数_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerアドレス * @param accountStorage AccountStorageコントラクト参照 |
| accountStorage | contract IAccountStorage |  |

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | version コントラクトバージョン |

### _adminOnly

```solidity
function _adminOnly(bytes32 hash, uint256 deadline, bytes signature) internal view
```

_Admin権限を持つかチェックする。署名からEOAを復元し権限を持つかチェックする(内部関数)。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| hash | bytes32 | signatureの計算元ハッシュ値 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 権限チェック対象の署名 |

### addAccount

```solidity
function addAccount(bytes32 accountId, string accountName, bytes32 validatorId) external
```

_共通領域 アカウント登録。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |
| accountName | string | アカウント名 |
| validatorId | bytes32 |  |

### modAccount

```solidity
function modAccount(bytes32 accountId, string accountName, bytes32 traceId) external
```

_アカウント名変更_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |
| accountName | string | アカウント名 |
| traceId | bytes32 | traceId |

### addAccountRole

```solidity
function addAccountRole(bytes32 accountId, address accountEoa, bytes32 traceId) external
```

_AccountのRoleを追加する。
```
emit event: AddAccountRole()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |
| accountEoa | address | accountEoa |
| traceId | bytes32 | トレースID |

### setAccountStatus

```solidity
function setAccountStatus(bytes32 accountId, bytes32 accountStatus, bytes32 reasonCode, bytes32 traceId) external
```

_Accountの有効性を更新する。(アクティブ or 凍結)
```
emit event: AccountEnabled()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |
| accountStatus | bytes32 | アカウントステータス |
| reasonCode | bytes32 | reasonCode |
| traceId | bytes32 |  |

### setTerminated

```solidity
function setTerminated(bytes32 accountId, bytes32 reasonCode, bytes32 traceId) external
```

_Accountの解約_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |
| reasonCode | bytes32 | reasonCode |
| traceId | bytes32 | トレースID |

### addZone

```solidity
function addZone(bytes32 accountId, uint16 zoneId, bytes32 traceId) external
```

_連携済みzone情報の追加_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |
| zoneId | uint16 | zoneId |
| traceId | bytes32 | トレースID |

### approve

```solidity
function approve(bytes32 ownerId, bytes32 spenderId, uint256 amount) external
```

_送金許可設定。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| ownerId | bytes32 | ownerId |
| spenderId | bytes32 | spenderId |
| amount | uint256 | 許容額 |

### mint

```solidity
function mint(bytes32 accountId, uint256 amount) external returns (uint256)
```

_発行。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |
| amount | uint256 | mint額 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | balance mint後の残高 |

### burn

```solidity
function burn(bytes32 accountId, uint256 amount) external returns (uint256)
```

_償却。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |
| amount | uint256 | burn額 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | balance burn後の残高 |

### forceBurn

```solidity
function forceBurn(bytes32 accountId, bytes32 traceId) external
```

_Accountの残高を強制償却する
最初にアカウントの凍結状態をisFrozenで確認し、凍結状態でない場合はエラーを返す_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |
| traceId | bytes32 | traceId |

### partialForceBurn

```solidity
function partialForceBurn(bytes32 accountId, uint256 burnedAmount, uint256 burnedBalance, bytes32 traceId) external
```

_AccountのBalanceを部分的に強制償却_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |
| burnedAmount | uint256 | 償却する金額 |
| burnedBalance | uint256 | 償却後に残す金額 |
| traceId | bytes32 | traceId |

### calcBalance

```solidity
function calcBalance(bytes32 fromAccount, bytes32 toAccount, uint256 amount) external returns (uint256 fromAccountBalance, uint256 toAccountBalance)
```

_FromからToへ送金指示。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| fromAccount | bytes32 | 送金元AccountのID |
| toAccount | bytes32 | 送金先AccountのID |
| amount | uint256 | 送金額 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| fromAccountBalance | uint256 | 送金元アカウントの残高 |
| toAccountBalance | uint256 | 送金先アカウントの残高 |

### calcAllowance

```solidity
function calcAllowance(bytes32 ownerId, bytes32 spenderId, uint256 amount) external
```

_送金許可額の減額を行う。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| ownerId | bytes32 | ownerId |
| spenderId | bytes32 | spenderId |
| amount | uint256 | 送金額 |

### editBalance

```solidity
function editBalance(bytes32 accountId, uint256 amount, uint256 calcPattern) external returns (uint256 balance)
```

_IssueVoucher, RedeemVoucherから呼ばれる残高の更新。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |
| amount | uint256 | 残高の更新額 |
| calcPattern | uint256 | 1:残高に加算 2:残高より減算 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| balance | uint256 | 更新後の残高 |

### setAccountAll

```solidity
function setAccountAll(struct AccountsAll account, uint256 deadline, bytes signature) external
```

_指定されたAccountIdに紐づくAccount情報を登録、もしくは上書きする
     バックアップリスト作業時のみ実行_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| account | struct AccountsAll | 全発行者データ |
| deadline | uint256 |  |
| signature | bytes |  |

### hasAccount

```solidity
function hasAccount(bytes32 accountId) external view returns (bool success, string err)
```

_AccountID存在確認。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:未登録, false:登録済 |
| err | string | エラーメッセージ |

### isActivated

```solidity
function isActivated(bytes32 accountId) external view returns (bool success, string err)
```

_Account有効状態確認_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:有効, false:無効、凍結済み |
| err | string |  |

### isTerminated

```solidity
function isTerminated(bytes32 accountId) external view returns (bool terminated, string err)
```

_Accountの解約フラグの確認をする。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| terminated | bool | true:アカウントが解約済, false:アカウントが未解約 |
| err | string | エラーメッセージ |

### _hasAccount

```solidity
function _hasAccount(bytes32 accountId) internal view returns (bool success, string err)
```

_AccountID存在確認 本体(内部関数)。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:未登録, false:登録済 |
| err | string | エラーメッセージ |

### balanceOf

```solidity
function balanceOf(bytes32 accountId) external view returns (uint256 balance, string err)
```

_残高の取得。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| balance | uint256 | 残高 |
| err | string | エラーメッセージ |

### getAccount

```solidity
function getAccount(bytes32 accountId) external view returns (struct AccountDataWithoutZoneId accountData, string err)
```

_Accountの情報を返す。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountData | struct AccountDataWithoutZoneId | アカウントデータ(zoneIdなし) |
| err | string |  |

### getDestinationAccount

```solidity
function getDestinationAccount(bytes32 accountId) external view returns (string accountName, string err)
```

_移転先のアカウント情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountName | string | アカウント名 |
| err | string | エラー |

### getAccountAll

```solidity
function getAccountAll(bytes32 accountId) external view returns (struct AccountDataAll accountDataAll, string err)
```

_Accountの全情報を返す。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataAll | struct AccountDataAll | アカウントデータ |
| err | string |  |

### getValidatorIdByAccountId

```solidity
function getValidatorIdByAccountId(bytes32 accountId) external view returns (bytes32 validatorId, string err)
```

_アカウントに紐づくバリデータIDを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | バリデータID |
| err | string | エラー |

### getAccountId

```solidity
function getAccountId(uint256 index) external view returns (bytes32 accountId, string err)
```

_IndexよりAccountIDを取得する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| index | uint256 | index |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |
| err | string | エラーメッセージ |

### getAccountLimit

```solidity
function getAccountLimit(bytes32 validatorId, bytes32 accountId) external view returns (struct FinancialZoneAccountData accountLimitData, string err)
```

_アカウントの限度額を取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitData | struct FinancialZoneAccountData |  |
| err | string |  |

### getAllowance

```solidity
function getAllowance(bytes32 ownerId, bytes32 spenderId) external view returns (uint256 allowance, uint256 approvedAt, string err)
```

_送金許可設定の取得。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| ownerId | bytes32 | ownerId |
| spenderId | bytes32 | spenderId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| allowance | uint256 |  |
| approvedAt | uint256 |  |
| err | string |  |

### getAllowanceList

```solidity
function getAllowanceList(bytes32 ownerId, uint256 offset, uint256 limit) external view returns (struct AccountApprovalAll[] approvalData, uint256 totalCount, string err)
```

_送金許可一覧照会 TODO:Core APIとのマッピング時に作成_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| ownerId | bytes32 | 送金許可元ID |
| offset | uint256 | オフセット |
| limit | uint256 | リミット |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| approvalData | struct AccountApprovalAll[] | 送金許可設定一覧 |
| totalCount | uint256 | 総数 |
| err | string | エラーメッセージ |

### getAccountCount

```solidity
function getAccountCount() external view returns (uint256 count)
```

_Accountの数を返却する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |

### getZoneByAccountId

```solidity
function getZoneByAccountId(bytes32 accountId) external view returns (struct ZoneData[] zones)
```

_Accountの連携済みのzoneIdを返却する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| zones | struct ZoneData[] | zoneIdのリスト |

### isFrozen

```solidity
function isFrozen(bytes32 accountId) external view returns (bool frozen, string err)
```

_Accountが凍結状態となっているか確認する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| frozen | bool | 凍結状態 |
| err | string | エラーメッセージ |

### getAccountsAll

```solidity
function getAccountsAll(uint256 index) external view returns (struct AccountsAll account)
```

_limitとoffsetで指定したAccountsを一括取得する_

### emitAfterBalance

```solidity
function emitAfterBalance(bytes32 fromAccountId, bytes32 toAccountId, bytes32 traceId) external
```

アカウントの残高情報を含むイベントを発行し、監査証跡を残す
        未登録アカウントの場合でもエラーにならず、空の残高情報でイベントを発行する

_残高照会後のイベントを発行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| fromAccountId | bytes32 | 送金元アカウントID（照会対象） |
| toAccountId | bytes32 | 送金先アカウントID（照会対象） |
| traceId | bytes32 | トレースID（イベントの追跡用） |

