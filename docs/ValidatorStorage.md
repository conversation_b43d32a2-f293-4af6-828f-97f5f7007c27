# Solidity API

## ValidatorStorage

_ValidatorStorageコントラクト
     Validatorデータのストレージ管理を行う
     CRUDのみを実装し、ビジネスロジックは含まない_

### validatorLogicOnly

```solidity
modifier validatorLogicOnly()
```

_ValidatorLogicコントラクトからのみ呼び出し可能を保証するmodifier_

### adminOnly

```solidity
modifier adminOnly(bytes32 hash, uint256 deadline, bytes signature)
```

_Admin権限を持つかチェックする。署名からEOAを復元し権限を持つかチェックする(modifier)。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| hash | bytes32 | signatureの計算元ハッシュ値 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 権限チェック対象の署名 |

### initialize

```solidity
function initialize(contract IContractManager contractManager, address validatorLogicAddr) public
```

_初期化関数_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerアドレス |
| validatorLogicAddr | address | ValidatorLogicコントラクトアドレス |

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | コントラクトバージョン |

### getValidatorData

```solidity
function getValidatorData(bytes32 validatorId) external view returns (struct ValidatorData validatorData)
```

_検証者データを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | 検証者ID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorData | struct ValidatorData | 検証者データ |

### setValidatorData

```solidity
function setValidatorData(bytes32 validatorId, struct ValidatorData validatorData) external
```

_検証者データを設定する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | 検証者ID |
| validatorData | struct ValidatorData | 検証者データ |

### updateValidatorName

```solidity
function updateValidatorName(bytes32 validatorId, bytes32 name) external
```

_検証者データの名前を更新する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | 検証者ID |
| name | bytes32 | 新しい名前 |

### updateValidatorRole

```solidity
function updateValidatorRole(bytes32 validatorId, bytes32 role) external
```

_検証者データのロールを更新する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | 検証者ID |
| role | bytes32 | 新しいロール |

### updateValidatorAccountId

```solidity
function updateValidatorAccountId(bytes32 validatorId, bytes32 validatorAccountId) external
```

_検証者データのバリデータアカウントIDを更新する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | 検証者ID |
| validatorAccountId | bytes32 | 新しいバリデータアカウントID |

### addAccountIdToValidator

```solidity
function addAccountIdToValidator(bytes32 validatorId, bytes32 accountId) external
```

_検証者にアカウントIDを追加する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | 検証者ID |
| accountId | bytes32 | 追加するアカウントID |

### addValidatorId

```solidity
function addValidatorId(bytes32 validatorId) external
```

_検証者IDを配列に追加する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | 追加する検証者ID |

### getValidatorIds

```solidity
function getValidatorIds() external view returns (bytes32[])
```

_検証者IDの配列を取得する_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes32[] | validatorIds 検証者IDの配列 |

### getValidatorIdByIndex

```solidity
function getValidatorIdByIndex(uint256 index) external view returns (bytes32)
```

_検証者IDを指定インデックスで取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| index | uint256 | インデックス |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes32 | validatorId 検証者ID |

### getValidatorIdsCount

```solidity
function getValidatorIdsCount() external view returns (uint256)
```

_検証者IDの総数を取得する_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | count 検証者IDの総数 |

### getValidatorIdExistence

```solidity
function getValidatorIdExistence(bytes32 validatorId) external view returns (bool)
```

_検証者IDの存在フラグを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | 検証者ID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bool | exists 存在フラグ |

### setValidatorIdExistence

```solidity
function setValidatorIdExistence(bytes32 validatorId, bool exists) external
```

_検証者IDの存在フラグを設定する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | 検証者ID |
| exists | bool | 存在フラグ |

### getAccountIdExistenceByValidatorId

```solidity
function getAccountIdExistenceByValidatorId(bytes32 validatorId, bytes32 accountId) external view returns (bool)
```

_検証者IDに紐づくアカウントIDの存在フラグを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | 検証者ID |
| accountId | bytes32 | アカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bool | exists 存在フラグ |

### setAccountIdExistenceByValidatorId

```solidity
function setAccountIdExistenceByValidatorId(bytes32 validatorId, bytes32 accountId, bool exists) external
```

_検証者IDに紐づくアカウントIDの存在フラグを設定する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | 検証者ID |
| accountId | bytes32 | アカウントID |
| exists | bool | 存在フラグ |

### getIssuerIdLinkedFlag

```solidity
function getIssuerIdLinkedFlag(bytes32 issuerId) external view returns (bool)
```

_発行者IDの紐付けフラグを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | 発行者ID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bool | linked 紐付けフラグ |

### setIssuerIdLinkedFlag

```solidity
function setIssuerIdLinkedFlag(bytes32 issuerId, bool linked) external
```

_発行者IDの紐付けフラグを設定する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | 発行者ID |
| linked | bool | 紐付けフラグ |

### setValidatorAll

```solidity
function setValidatorAll(struct ValidatorAll validator, uint256 deadline, bytes signature) external
```

_バックアップ用に全検証者データを設定する（Admin権限必要）_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validator | struct ValidatorAll | 全検証者データ |
| deadline | uint256 | 署名の期限 |
| signature | bytes | Admin署名 |

### getValidatorAll

```solidity
function getValidatorAll(uint256 index) external view returns (struct ValidatorAll validator)
```

_バックアップ用に検証者データを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| index | uint256 | インデックス |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| validator | struct ValidatorAll | 全検証者データ |

### setContractManagerAddress

```solidity
function setContractManagerAddress(address contractManagerAddr) external
```

_ContractManagerアドレスを更新する（将来の拡張用）_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManagerAddr | address | 新しいContractManagerアドレス |

### getContractManagerAddress

```solidity
function getContractManagerAddress() external view returns (address)
```

_ContractManagerアドレスを取得する_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | address | contractManagerAddr ContractManagerアドレス |

### setValidatorLogicAddress

```solidity
function setValidatorLogicAddress(address validatorLogicAddr) external
```

_ValidatorLogicアドレスを更新する（Admin権限必要）_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorLogicAddr | address | 新しいValidatorLogicアドレス |

