# Solidity API

## IBCToken

### initialize

```solidity
function initialize(contract IContractManager contractManager) public
```

_Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerアドレス |

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | コントラクトバージョン |

### redeemVoucher

```solidity
function redeemVoucher(bytes32 accountId, uint256 amount, bytes32 traceId) external
```

_Voucherを償却する。

```
emit event: RedeemVoucher()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |
| amount | uint256 | 償却額 |
| traceId | bytes32 | トレースID |

### issueVoucher

```solidity
function issueVoucher(bytes32 accountId, uint256 amount, bytes32 traceId) external
```

_Voucherを発行する。

```
emit event: IssueVoucher()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |
| amount | uint256 | 発行額 |
| traceId | bytes32 | トレースID |

### discharge

```solidity
function discharge(bytes32 accountId, uint16 fromZoneId, uint256 amount, bytes32 traceId) external
```

_後続のDischarge処理を開始するためのイベントを発行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| fromZoneId | uint16 | ディスチャージ元のゾーンID |
| amount | uint256 | ディスチャージ額 |
| traceId | bytes32 | トレースID |

### transferFromEscrow

```solidity
function transferFromEscrow(uint16 fromZoneId, bytes32 sendAccountId, bytes32 fromAccountId, bytes32 toAccountId, uint256 amount, bytes32 traceId) external
```

_IBC用 Escrow --> Account。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| fromZoneId | uint16 |  |
| sendAccountId | bytes32 | sendAccountId |
| fromAccountId | bytes32 | fromAccountId |
| toAccountId | bytes32 | toAccountId |
| amount | uint256 | 送金額 |
| traceId | bytes32 | トレースID |

### transferToEscrow

```solidity
function transferToEscrow(uint16 fromZoneId, bytes32 fromAccountId, bytes32 toAccountId, uint256 amount, bytes32 traceId) external
```

_IBC用 Account --> Escrow。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| fromZoneId | uint16 |  |
| fromAccountId | bytes32 | fromAccountId |
| toAccountId | bytes32 | toAccountId |
| amount | uint256 | 送金額 |
| traceId | bytes32 | トレースID |

### _localTransfer

```solidity
function _localTransfer(struct TransferData data, bytes32 traceId) internal
```

_送金(内部関数)。

```
emit event: Transfer()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| data | struct TransferData | TransferData |
| traceId | bytes32 |  |

### syncBusinessZoneBalance

```solidity
function syncBusinessZoneBalance(struct SyncBuisinessZoneBlanaceParams params) external
```

_IBC用　残高同期処理_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| params | struct SyncBuisinessZoneBlanaceParams | BizZone内送金の残高更新のデータ |

### initAccountBalance

```solidity
function initAccountBalance(bytes32 accountId) external
```

_Biz Zone のアカウントの残高を初期化(0)する。（強制償却済みのみ）_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |

### checkAdminRole

```solidity
function checkAdminRole(bytes32 hash, uint256 deadline, bytes signature) external view returns (bool has, string err)
```

_IBC用 Admin権限チェック。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| hash | bytes32 | signatureの計算元ハッシュ値 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 権限チェック対象の署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| has | bool | true:権限あり,false:権限なし |
| err | string | エラーメッセージ |

